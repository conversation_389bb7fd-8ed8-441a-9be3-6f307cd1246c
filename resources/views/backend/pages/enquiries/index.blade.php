@extends('backend.layout.master')
@section('page-header')
@endsection
@section('content')

    <style type="text/css">
        .val-error {
            color: red;
        }

        .modal-dialog-full {
            max-width: 6400px !important;
            width: 100% !important;
        }

        .select2-search__field {
            width: 100% !important;
        }

        .filter-round-btn {
            height: fit-content;
            min-height: 38px;
        }

        .ks-izi-modal-trigger2 {

        }

        #btnActions ul li a {
            height: fit-content;
            width: 100%;
            /* padding: 0; */
            display: inline-flex;
        }

        .import-info strong {
            font-weight: 600;
        }
    </style>
    {{-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css"> --}}
    {{-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/izitoast/1.1.1/css/iziToast.min.css"/> --}}
    {{-- <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/izimodal/1.4.2/css/iziModal.css"/> --}}
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>
    {{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script> --}}
    {{-- <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.2.1/jquery.min.js"></script> --}}
    <main class="main-wrapper">
        @if(session('flash_notification'))
            <input type="hidden" name="" value="{{ session('flash_notification') }}" id="flash-notis">
        @endif
        <!--   / Side menu included /  -->
        @include ('backend.layout.sidebar-v2.crmsidebar-v2')
        <div class="task-panel">
            <div class="fxs">
                <div class="row justify-content-between ">
                    <div class="">

                        {{-- <div class="current-page">
                  <p><a href="{{ url()->previous() }}">Settings</a>>  Account settings</p>
               </div> --}}

                        @if(request("follow_up_leads"))
                            <h5> Followup Required
                            </h5>
                        @elseif(request("unassigned"))
                            <div class="add-y-team">
                                <div class="y-team-header y-team-header-V2">
                                    <a href="{{ url()->previous() }}">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25"
                                             viewBox="0 0 24 25" fill="none">
                                            <path d="M10.875 19.3864L4.275 12.7864C4.175 12.6864 4.104 12.578 4.062 12.4614C4.02067 12.3447 4 12.2197 4 12.0864C4 11.953 4.02067 11.828 4.062 11.7114C4.104 11.5947 4.175 11.4864 4.275 11.3864L10.875 4.78636C11.0583 4.60302 11.2873 4.50702 11.562 4.49836C11.8373 4.49036 12.075 4.58636 12.275 4.78636C12.475 4.96969 12.5793 5.19869 12.588 5.47336C12.596 5.74869 12.5 5.98636 12.3 6.18636L7.4 11.0864H18.575C18.8583 11.0864 19.096 11.182 19.288 11.3734C19.4793 11.5654 19.575 11.803 19.575 12.0864C19.575 12.3697 19.4793 12.607 19.288 12.7984C19.096 12.9904 18.8583 13.0864 18.575 13.0864H7.4L12.3 17.9864C12.4833 18.1697 12.5793 18.403 12.588 18.6864C12.596 18.9697 12.5 19.203 12.3 19.3864C12.1167 19.5864 11.8833 19.6864 11.6 19.6864C11.3167 19.6864 11.075 19.5864 10.875 19.3864Z"
                                                  fill="#4D5459"/>
                                        </svg>
                                        UnAssinged Leads
                                    </a>
                                </div>
                            </div>
                        @elseif(request("yet_to_contacts"))
                            <h5>Yet to Contact</h5>
                        @elseif(request("attention"))
                            <h5>Spotlight</h5>
                        @elseif(request("campaign_id"))
                            <div class="add-y-team">
                                <div class="y-team-header y-team-header-V2">
                                    <a href="{{ url()->previous() }}">
                                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25"
                                             viewBox="0 0 24 25" fill="none">
                                            <path d="M10.875 19.3864L4.275 12.7864C4.175 12.6864 4.104 12.578 4.062 12.4614C4.02067 12.3447 4 12.2197 4 12.0864C4 11.953 4.02067 11.828 4.062 11.7114C4.104 11.5947 4.175 11.4864 4.275 11.3864L10.875 4.78636C11.0583 4.60302 11.2873 4.50702 11.562 4.49836C11.8373 4.49036 12.075 4.58636 12.275 4.78636C12.475 4.96969 12.5793 5.19869 12.588 5.47336C12.596 5.74869 12.5 5.98636 12.3 6.18636L7.4 11.0864H18.575C18.8583 11.0864 19.096 11.182 19.288 11.3734C19.4793 11.5654 19.575 11.803 19.575 12.0864C19.575 12.3697 19.4793 12.607 19.288 12.7984C19.096 12.9904 18.8583 13.0864 18.575 13.0864H7.4L12.3 17.9864C12.4833 18.1697 12.5793 18.403 12.588 18.6864C12.596 18.9697 12.5 19.203 12.3 19.3864C12.1167 19.5864 11.8833 19.6864 11.6 19.6864C11.3167 19.6864 11.075 19.5864 10.875 19.3864Z"
                                                  fill="#4D5459"/>
                                        </svg>
                                        Campaign

                                    </a>
                                </div>
                            </div>
                            <h5> Campaign Leads </h5>
                        @else
                            <h5>Leads</h5>
                        @endif

                        {{-- <h5>@if(request("follow_up_leads")) Followup Required @elseif(request("yet_to_contacts")) Yet to
                Contact @elseif(request("campaign_id")) Campaign  @endif Leads
             </h5> --}}
                        <p>Potential customers showing interest in a product or service.</p>
                    </div>
                    <div class="row-wrap align-items-center">
                        <div class="tbl-h-details">
                            <!-- <div class="main-round-btn" onclick="clearfields()">
                     <a data-toggle="modal" data-target="#update_status" id="ass_agentAllEnq"
                        href="#">
                         <i class="fa fa-star"></i>Update Status</a>
                     </div> -->
                            <!-- <div class="main-round-btn">
                     <a data-toggle="modal" data-target="#ass_agent" id="ass_agentAllEnq"
                        href="#">
                         <i class="fa fa-user"></i>Assign Agent</a>
                     </div> -->
                            @if(!request("follow_up_leads"))
                                @if ($campaign_id == 0)
                                    @if(!request("attention"))
                                        <div>
                                            <div class="main-round-btn green-btn">
                                                <a {{-- data-toggle="modal" data-target="#ks-izi-modal-large"--}} id="addEnquiry"
                                                   href="{{url('/user/enquiries/create')}}">
                                                    <i class="fa fa-plus"></i>Add Lead
                                                </a>
                                            </div>
                                        </div>
                                    @endif
                                @endif
                            @endif
                            <!-- <div class="main-round-btn">
                     <a data-toggle="modal" data-target="#enquiry-upload-modal" id="uploadEnquiry"
                        href="#"><i
                                 class="fa fa-upload"></i> Contacts </a>
                     </div> -->
                            @if(Auth::user()->int_role_id==2)
                                <!-- <div class="main-round-btn">
                     <a id="whatsAppCon" onclick="WhatsappModal()"
                        href="#"><i
                                 class="fa fa-whatsapp"></i> Connect</a>
                     </div> -->
                            @endif
                            <div>
                                <a data-toggle="collapse" href="#collapseExample" title="Filter" role="button"
                                   aria-expanded="false" aria-controls="collapseExample">
                                    <div class="main-round-btn main-filter-btn">
                                        <i class="fa fa-filter"></i>Filter
                                    </div>
                                </a>
                            </div>
                            <div class="text-right chart-filter">
                                <div class="input-group with-addon-icon-left" style="display:none">
                                    <select class="form-control "
                                            style="    height: 36px;font-size: 12px;font-weight: 500;color: #1d1d1d;margin-right:30px"
                                            id="sort_by">
                                        <option value="1" @if(!request("attention")) selected @endif>Created Date
                                        </option>
                                        <option value="2" @if(request("attention")) selected @endif>Updated Date
                                        </option>
                                    </select>
                                </div>
                                <div id="btnActions">
                                    <a class="main-round-btn main-filter-btn" href="#" data-toggle="dropdown"
                                       aria-haspopup="true" aria-expanded="false">
                                        <i class="fa fa-sort"></i>Sort by</a>
                                    <div class="dropdown-menu dropdown-menu-sm dropdown-menu-right">
                                        <ul class="navi navi-hover">
                                            <li class="navi-item" onclick="sortBy(1)">
                                                <a href="#" class="navi-link py-2">
                                                    <span class="navi-text" id="sort_By1"> Created Date <i
                                                                class="fa fa-check pl-2 sort_check"
                                                                @if(request("attention")) style="display:none" @endif></i></span>
                                                </a>
                                            </li>
                                            <li class="navi-item" onclick="sortBy(2)">
                                                <a href="#" class="navi-link py-2">
                                                    <span class="navi-text" id="sort_By2">Updated Date <i
                                                                class="fa fa-check pl-2 sort_check"
                                                                @if(!request("attention")) style="display:none" @endif></i></span>
                                                </a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            {{csrf_field()}}
                            <div id="btnActions">
                                <a class="main-round-btn main-action-btn" href="#" data-toggle="dropdown"
                                   aria-haspopup="true" aria-expanded="false">
                                    <i class="fa fa-cogs"></i>Actions
                                </a>
                                <div class="dropdown-menu dropdown-menu-sm dropdown-menu-right">
                                    <ul class="navi navi-hover">
                                        <li class="navi-item" onclick="clearfields()">
                                            <a href="#" data-toggle="modal" data-target="#bulk_update"
                                               id="bulk_update_li" class="navi-link py-2">
                                                <span class="navi-text"> <i class="fa fa-list"></i> Bulk Update</span>
                                            </a>
                                        </li>
                                        <li class="navi-item" onclick="clearfields()">
                                            <a href="#" data-toggle="modal" data-target="#update_status"
                                               id="ass_agentAllEnq" class="navi-link py-2">
                                                <span class="navi-text"> <i class="fa fa-star"></i> Update status</span>
                                            </a>
                                        </li>
                                        @if(!request("campaign_id")|| $campaign_type==1)
                                            <li class="navi-item">
                                                <a href="#" data-toggle="modal" data-target="#ass_agent"
                                                   id="ass_agentAllEnq" class="navi-link py-2">
                                                    <span class="navi-text"><i
                                                                class="fa fa-user"></i> Assign agent</span>
                                                </a>
                                            </li>
                                        @endif
                                        @if(!request("campaign_id"))
                                            <li class="navi-item">
                                                <a href="{{url('user/bulk-assign-enquiries')}}" class="navi-link py-2">
                                                    <span class="navi-text"><i
                                                                class="fa fa-users"></i> Bulk Assign</span>
                                                </a>
                                            </li>
                                        @endif
                                        @if ($showImportContact)
                                            <li class="navi-item">
                                                <a href="{{url('/user/import-requests')}}" class="navi-link py-2">
                                                    <span class="navi-text">
                                                        <i>
                                                            <img src="/backend/images/file-import-solid.svg" width="11px">
                                                        </i> 
                                                        Import contacts
                                                    </span>
                                                </a>
                                            </li>
                                        @else
                                            @if(!request("follow_up_leads"))
                                                @if(!request("unassigned") == "true")
                                                    <li class="navi-item">
                                                        <a href="#" data-toggle="modal" data-target="#enquiry-upload-modal"
                                                        id="uploadEnquiry" class="navi-link py-2">
                                                            <span class="navi-text"> <i><img
                                                                            src="/backend/images/file-import-solid.svg"
                                                                            width="11px"></i> Import contacts V1</span>
                                                        </a>
                                                    </li>
                                                @endif
                                            @endif    
                                        @endif
                                        <li class="navi-item">
                                            <a href="#" id="btnCreateCampaign" data-toggle="modal"
                                               data-target="#assign_campaign" class="navi-link py-2">
                                                <span class="navi-text"> <i><img src="/backend/images/add-to-campaign.svg" width="11px"></i>
                                                    @if(request("unassigned") == "true")
                                                        Add to Data Pool
                                                    @else
                                                        Add to Campaign
                                                    @endif
                                                </span>
                                            </a>
                                        </li>
                                        <li class="navi-item">
                                            <a href="#" id="btnCreateCallTask" class="navi-link py-2">
                                                <span class="navi-text"> <i><img src="/backend/images/call-task.svg" width="11px"></i> Create Call Task </span>
                                            </a>
                                        </li>
                                        @if((Auth::user()->int_role_id==\App\User::USERS || Auth::user()->is_co_admin==1) && !request("campaign_id"))
                                            <li class="navi-item" onclick="deleteMultiple()">
                                                <form id="delete_multiple_lead">
                                                    {{csrf_field()}}
                                                    <input type="hidden" id="campaign_id" name="campaign_id"
                                                           value="{{request('campaign_id')}}">
                                                    <input type="hidden" id="id_leads" name="lead_ids">
                                                    <a href="#" id="delete_leads" class="navi-link py-2">
                                                        <span class="navi-text"><i
                                                                    class="fa fa-trash"></i> Delete</span>
                                                    </a>
                                                </form>
                                            </li>
                                        @endif
                                        @if(request("campaign_id") && request("unassigned") != 'true')
                                            <li class="navi-item" onclick="addLeads({{request('campaign_id')}})">
                                                <a href="#" data-toggle="modal" data-target="#addLeads" id="add_leads"
                                                   class="navi-link py-2">
                                                    <span class="navi-text"> <i
                                                                class="fa fa-money"></i> Add leads</span>
                                                </a>
                                            </li>
                                            <li class="navi-item" onclick="campaignTaskRemove()">
                                                @if((Auth::user()->int_role_id==\App\User::STAFF || Auth::user()->is_co_admin !=1))
                                                    <form id="delete_multiple_lead">
                                                        {{csrf_field()}}
                                                        <input type="hidden" id="campaign_id" name="campaign_id"
                                                               value="{{request('campaign_id')}}">
                                                        <input type="hidden" id="id_leads" name="lead_ids">
                                                        <a href="#" data-toggle="modal"
                                                           data-target="#campaign_task_remove" id="ass_agentAllEnq"
                                                           class="navi-link py-2">
                                                            <span class="navi-text"> <i class="fa fa-money"></i> Remove Lead</span>
                                                        </a>
                                                    </form>
                                                @else
                                                    <a href="#" data-toggle="modal" data-target="#campaign_task_remove"
                                                       id="ass_agentAllEnq" class="navi-link py-2">
                                                        <span class="navi-text"> <i class="fa fa-money"></i> Remove Lead</span>
                                                    </a>
                                                @endif
                                            </li>
                                        @endif
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="layout-wrapper">
            <div class="content-section">
                <div class="">
                    <div class="col-lg-4 col-md-5">
                    </div>
                    <div class="mb-2 form-group ">
                        <div class="collapse" id="collapseExample">
                            <div class="filter-container bg-light-grey">
                                <form class="" id="getReport" enctype="multipart/form-data">
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <div class="input-group with-addon-icon-left" id="range_date">
                                                <input type="text" class="form-control date_picker"
                                                       placeholder="Date" name="all_date" autocomplete="off"
                                                       id="all_date">
                                                <span class="input-group-append">
                                 <span class="input-group-text">
                                 <i class="fa fa-calendar"></i>
                                 </span>
                                 </span>
                                                <div class="invalid-feedback"></div>
                                            </div>
                                        </div>
                                        <div class="col-sm-3">
                                            <select class="form-control select2" style="width: 100%; height: 100%"
                                                    id="dateBy">
                                                <option value="">Filter by date</option>
                                                @foreach($date_by as $dateBy)
                                                    <option value="{{$dateBy['id']}}">{{$dateBy['name']}}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-sm-3">
                                            <select class="form-control select2" style="width: 100%; height: 100%"
                                                    id="enquiryTypeId">
                                                <option value="">Select Enquiry Source</option>
                                                <option value="-1">Empty Source</option>
                                                @foreach($types as $type)
                                                    <option value="{{$type->pk_int_enquiry_type_id}}">{{$type->vchr_enquiry_type}}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        {{-- campaign_id passing:start --}}
                                        <input type="hidden" name="campaign_id" id="campaign_id"
                                               value="{{ $campaign_id }}">
                                        <div class="col-sm-3">
                                            <select class=" form-control select2" style="width: 100%; height: 100%"
                                                    id="enquiryPurposeId">
                                                <option value="">Select Enquiry Purpose</option>
                                                <option value="-1">Empty Purpose</option>
                                                @foreach($purposes as $purpose)
                                                    <option value="{{$purpose->pk_int_purpose_id}}">{{$purpose->vchr_purpose}}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-sm-3">
                                            <select class=" form-control select2" style="width: 100%; height: 100%"
                                                    id="enquiryStatusId">
                                                <option value="">Select Lead Status</option>
                                                <option value="-1">Empty Lead Status</option>
                                                @foreach($enquiry_status as $status)
                                                    <option value="{{$status->pk_int_feedback_status_id}}">{{$status->vchr_status}}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        @if(App\User::getVendorId() == App\Common\Variables::BAZANI_USER_ID)
                                            @if($district->count() > 0)
                                                <div class="col-sm-3">
                                                    <select class=" form-control select2"
                                                            style="width: 100%; height: 100%"
                                                            id="districtId">
                                                        <option value="">Select District</option>
                                                        @foreach($district as $dis)
                                                            <option value="{{$dis->id}}">{{$dis->name}}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            @endif
                                        @endif

                                        @if(App\Common\Variables::checkEnableSettings('branch-filter'))
                                            <div class="col-sm-3">
                                                <select name="branch_id" id="branch_id" class="form-control">
                                                    <option value="">Select Branch</option>
                                                    @foreach($branches as $branch)
                                                        <option value="{{$branch->id}}">{{$branch->branch}}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        @else
                                            <input type="hidden" name="branch_id" id="branch_id">
                                        @endif

                                        <div class="col-sm-3">
                                            <select class=" form-control"
                                                    id="followup_added">
                                                <option value="">Followup Added</option>
                                                <option value="">All
                                                </option>
                                                <option value="2">No
                                                </option>
                                                <option value="1">Yes
                                                </option>
                                            </select>
                                        </div>
                                        <div class="col-sm-3">
                                            <select class="form-control select2" id="created_by"
                                                    style="width: 100%; height: 100%" id="enquiryTypeId">
                                                <option value="">Created By</option>
                                                {{-- <option value="{{$created->pk_int_user_id}}">{{$created->vchr_user_name}} --}}
                                                </option>
                                                @foreach($created_bys as $created_by)
                                                    <option value="{{$created_by->pk_int_user_id}}">{{$created_by->vchr_user_name}}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        @if (Auth::user()->int_role_id == App\User::USERS || Auth::user()->int_role_id ==
                           App\User::ADMIN || Auth::user()->int_role_id == App\User::STAFF)
                                            <div class="col-sm-3">
                                                <select class="form-control select2" id="staff_id"
                                                        style="width: 100%; height: 100%"
                                                        id="enquiryTypeId">
                                                    <option value="">Assigned To</option>
                                                    <option value="unassigned">UnAssigned</option>
                                                    @foreach($staffId as $staff_id)
                                                        <option value="{{$staff_id->pk_int_user_id}}">{{$staff_id->vchr_user_name}}
                                                        </option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        @endif
                                        <div class="col-sm-3">
                                            <select class=" form-control select2" style="width: 100%; height: 100%"
                                                    id="leadTypeId">
                                                <option value="">Select Lead Type</option>
                                                <option value="-1">Empty Lead Type</option>
                                                @foreach($leadTypes as $lead_type)
                                                    <option value="{{$lead_type->id}}">{{$lead_type->name}}
                                                    </option>
                                                @endforeach
                                            </select>
                                        </div>
                                        @if ($dids && in_array(\App\User::getVendorId(), [4124,4510]))
                                            {{-- currently vendor depends functionality. --}}
                                            <div class="col-sm-3">
                                                <select class=" form-control" name="did_filter" id="did_filter">
                                                    <option value="">Select DID</option>
                                                    @foreach($dids as $did)
                                                        <option value="{{ $did->did }}">{{ $did->did }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        @endif
                                        @if(request('campaign_id'))
                                            <div class="col-sm-3">
                                                <select class=" form-control select2" style="width: 100%; height: 100%"
                                                        id="campaign_status">
                                                    <option value="">Select Campaign Status</option>
                                                    <option value="1">Completed
                                                    </option>
                                                    <option value="-1">Pending
                                                    </option>
                                                </select>
                                            </div>
                                        @endif
                                        @if(count($additional_fields))
                                            @foreach($additional_fields as $add_field)
                                                @if($add_field->show_in_filter==1)
                                                    @if( $add_field->input_type==1)
                                                        <div class="col-sm-3 {{ 'prps_'.$add_field->id}} {{ $add_field->additionalPurpose()->exists() ? 'd-none' : ''}}">
                                                            <div class="form-group">
                                                                <input type="text" class="form-control add_flds"
                                                                       name="{{$add_field->id}}"
                                                                       placeholder="{{$add_field->field_name}}"
                                                                       autocomplete="off">
                                                            </div>
                                                        </div>
                                                    @elseif( $add_field->input_type==3 || $add_field->input_type==5)
                                                        <div class="col-sm-3 {{ 'prps_'.$add_field->id}} {{ $add_field->additionalPurpose()->exists() ? 'd-none' : ''}}">
                                                            <div class="form-group">
                                                                <input type="text" class="form-control add_flds"
                                                                       name="{{$add_field->id}}"
                                                                       placeholder="{{$add_field->field_name}}"
                                                                       autocomplete="off" onfocus="(this.type='date')"
                                                                       onblur="(this.type='text')">
                                                            </div>
                                                        </div>
                                                    @elseif($add_field->input_type==2)
                                                        <div class="col-sm-3 {{ 'prps_'.$add_field->id}} {{ $add_field->additionalPurpose()->exists() ? 'd-none' : ''}}">
                                                            <select class="form-control select2 add_flds"
                                                                    style="width: 100%; height: 100%"
                                                                    name="{{$add_field->id}}"
                                                                    id="enquiryTypeSelect">
                                                                <option value="">
                                                                    Select {{$add_field->field_name}}
                                                                </option>
                                                                @if($add_field->values)
                                                                    @foreach($add_field->values as $key=>$value)
                                                                        <option value="{{$value }}"
                                                                        >{{$value}}</option>
                                                                    @endforeach
                                                                @endif
                                                            </select>
                                                            <div class="error fk_int_enquiry_type_id ">
                                                                <span class="muted"></span>
                                                            </div>
                                                        </div>
                                                    @elseif($add_field->input_type==8)
                                                        <!-- Multi Select -->
                                                        <div class="col-sm-3 {{ 'prps_'.$add_field->id}} {{ $add_field->additionalPurpose()->exists() ? 'd-none' : ''}}">
                                                            <select class="form-control select2 add_flds"
                                                                    style="width: 100%; height: 100%" multiple
                                                                    name="{{$add_field->id}}"
                                                                    data-placeholder=" Select {{$add_field->field_name}}"
                                                                    id="enquiryTypeSelect">
                                                                {{--
                                 <option value="">
                                    Select {{$add_field->field_name}}
                                 </option>
                                 --}}
                                                                @foreach($add_field->decoded_values as $key=>$value)
                                                                    <option value="{{$value }}"
                                                                    >{{$value }}</option>
                                                                @endforeach
                                                            </select>
                                                            <div class="error fk_int_enquiry_type_id ">
                                                                <span class="muted"></span>
                                                            </div>
                                                        </div>
                                                    @else
                                                        <div class="col-sm-3 {{ 'prps_'.$add_field->id}} {{ $add_field->additionalPurpose()->exists() ? 'd-none' : ''}}">
                                                            <div class="form-group">
                                                                <input type="text" class="form-control add_flds"
                                                                       name="{{$add_field->id}}"
                                                                       placeholder="{{$add_field->field_name}}"
                                                                       autocomplete="off">
                                                            </div>
                                                        </div>
                                                    @endif
                                                @endif
                                            @endforeach
                                        @endif
                                        <div class="filter-round-btn ml-3">
                                            <a href="javascript:function() { return false; }" role="button"
                                               onclick="filterData()"
                                               aria-expanded="false" id="filter">Filter</a>
                                        </div>
                                        <div class="filter-round-btn ml-3">
                                            <a role="button" onclick="clearFilter()"
                                               aria-expanded="false" id="clear">Clear</a>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="gl-table table-responsive">
                    <!--  /Your content goes here/ -->
                    <table id="enquiry-info-table" class="table table-striped table-bordered nowrap table-custom"
                           cellspacing="0" role="grid">
                        <thead>
                        <tr>
                            <th>
                                &nbsp;&nbsp;&nbsp;<input type="checkbox" id="checkAll" class="checkAll">
                            </th>
                            <th><i class="fa fa-cogs sort-pop" data-toggle="modal"
                                   data-target=".bd-example-modal-lg"></i></th>
                            <!-- <th><i class="fa fa-cogs sort-pop" ></i></th> -->
                            <!-- <th>Sl No</th> -->
                            <th>Name</th>
                            {{--
                     <th>Company</th>
                     --}}
                            <th>Phone</th>
                            <th>Assigned To</th>
                            <th>Purpose</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Source</th>
                            <th>Email</th>
                            <th>Address</th>
                            <th>Mobile</th>
                            <th>Created At</th>
                            <th>Updated At</th>
                            <th>Created By</th>
                            <th>Next follow up</th>
                            <th>Assigned Date</th>
                            @foreach($additional_fields as $add_field)
                                @if($add_field->show_in_list==1)
                                    <th>{{$add_field->field_name}}</th>
                                @endif
                            @endforeach
                        </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </main>
    <!------------ /add modal/  -------------->
    <div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form id="enquirySubmit">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content ">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Add Enquiry</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3 row">
                        <div class="md-form mb-1 col-md-6">
                            <label for="default-input" class="form-control-label">Enquiry Source</label>
                            <select class="form-control" name="fk_int_enquiry_type_id" id="enquiryTypeSelect">
                                <option value="">Select Lead source</option>
                                @foreach($types as $key=>$type)
                                    <option value="{{ $type->pk_int_enquiry_type_id }}">{{ $type->vchr_enquiry_type }}</option>
                                @endforeach
                            </select>
                            <div class="error fk_int_enquiry_type_id ">
                                <span class="muted"></span>
                            </div>
                        </div>
                        <div class="md-form mb-1 col-md-6">
                            <label for="default-input" class="form-control-label">Enquiry Purpose</label>
                            @if(count($purposes)==0)
                                <a class="bg-blue bdr1 bdr-blue mg-lft-25 clr-white" title="Add Enquiry Purpose "
                                   href="{{url('/user/enquiry-purpose')}}"> &nbsp;<i class="fa fa-plus"
                                                                                     aria-hidden="true"></i>&nbsp;</a>
                            @endif
                            <select class="form-control enquiryPurposeSelect" name="fk_int_purpose_id">
                                <option value="">Select Lead purpose</option>
                                @foreach($purposes as $key=>$purpose)
                                    <option value="{{ $purpose->pk_int_purpose_id }}">{{ $purpose->vchr_purpose }}</option>
                                @endforeach
                            </select>
                            <div class="error fk_int_purpose_id ">
                                <span class="muted"></span>
                            </div>
                        </div>
                        <div class="md-form mb-1 col-md-6">
                            <label for="default-input" class="form-control-label">Lead Status</label>
                            <select class="form-control" name="feedback_status" id="feedback_status">
                                <option value="">Select Status</option>
                                @foreach($enquiry_status as $status)
                                    <option value="{{ $status->pk_int_feedback_status_id }}">{{ $status->vchr_status }}</option>
                                @endforeach
                            </select>
                            <div class="error feedback_status">
                                <span class="muted"></span>
                            </div>
                        </div>
                        <div class="md-form mb-1 col-md-6">
                            <label for="default-input" class="form-control-label">Customer Name</label>
                            <input type="text" name="vchr_customer_name" class="form-control" placeholder="">
                            <div class="error vchr_customer_name ">
                                <span class="muted"></span>
                            </div>
                        </div>
                        <div class="md-form mb-1 col-md-6">
                            <label for="default-input" class="form-control-label">Customer Company Name</label>
                            <input type="text" name="vchr_customer_company_name" class="form-control" placeholder="">
                            <div class="error vchr_customer_company_name ">
                                <span class="muted"></span>
                            </div>
                        </div>
                        <div class="md-form mb-1 col-md-6 ">
                            <label for="default-input" class="form-control-label">Customer Phone1</label>
                            <br>
                            <div class="row m-0">
                                <select name="country_code" id="" class="form-control col-md-6">
                                    @foreach($countrys as $country)
                                        <option data-countryCode="{{ $country->country_code }}"
                                                value="{{ $country->code }}">(+{{ $country->code  }})
                                        </option>
                                    @endforeach
                                </select>
                                <input type="text" name="vchr_customer_mobile" class="form-control col-md-6"
                                       placeholder="Enter Phone Number" id="idForCC">
                                <div class="error vchr_customer_mobile ">
                                    <span class="muted"></span>
                                </div>
                            </div>
                        </div>
                        <div class="md-form mb-1 col-md-6">
                            <label for="default-input" class="form-control-label">Landline Number</label>
                            <input type="text" name="landline_number" class="form-control"
                                   placeholder="Landline Number">
                            <div class="error landline_number">
                                <span class="muted"></span>
                            </div>
                        </div>
                        <!-- More Phone Numbers -->
                        <div class="md-form mb-1 col-md-6">
                            <label for="default-input" class="form-control-label">More Phone Numbers</label>
                            <div id="row_add_phone">
                                <div class="row m-0">
                                    <input type="integer" name="more_phone_numbers[]" class="form-control col-md-11 "
                                           placeholder="Phone Number">
                                    <a href="javascript:void(0);" class="add_field_button" title="Add field"><i
                                                class="fa fa-plus-circle col-md-0 ml-1 mt-2"
                                                style="font-size:18px;color:green"
                                                id="add_phone"></i></a>
                                    <div class="error more_phone_numbers ">
                                        <span class="muted"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="md-form mb-1 col-md-6">
                            <label for="default-input" class="form-control-label">Date of Birth</label>
                            <input type="date" name="date_of_birth" class="form-control" placeholder="Date of birth">
                            <div class="error date_of_birth ">
                                <span class="muted"></span>
                            </div>
                        </div>
                        <div class="md-form mb-1 col-md-6">
                            <label for="default-input" class="form-control-label">Customer Email</label>
                            <input type="text" name="vchr_customer_email" class="form-control" placeholder="">
                            <div class="error vchr_customer_email ">
                                <span class="muted"></span>
                            </div>
                        </div>
                        <div class="md-form mb-1 col-md-6">
                            <label for="default-input" class="form-control-label">Customer Feedback</label>
                            <input name="vchr_enquiry_feedback" class="form-control" type="text">
                            <div class="error vchr_enquiry_feedback ">
                                <span class="muted"></span>
                            </div>
                        </div>
                        @if (Auth::user()->int_role_id == App\User::USERS || Auth::user()->int_role_id == App\User::ADMIN)
                            <div class="md-form mb-1 col-md-6">
                                <label for="default-input" class="form-control-label">Agent</label>
                                <select class="form-control" name="staff_id" id="staff_id">
                                    <option value="">Select Agent</option>
                                    @foreach($staffId as $key=>$staff_id)
                                        <option value="{{ $staff_id->pk_int_user_id }}">{{ $staff_id->vchr_user_name }}</option>
                                    @endforeach
                                </select>
                                <div class="error staff_id ">
                                    <span class="muted"></span>
                                </div>
                            </div>
                        @else
                            {{--  <input type="hidden" name="staff_id" value="{{ Auth::user()->pk_int_user_id }}" --}}
                        @endif
                        <div class="modal-footer d-flex justify-content-center pull-right">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <button class="btn btn-deep-orange demo-btn mt-3">Add</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!------------ /update modal/  -------------->
    @include('backend.pages.enquiries.enquiry-edit-modal')
    {{-- @include('backend.pages.enquiries.enquiry-detail-modal') --}}
    <!------------ /Import Enquiries modal/  -------------->
    <div class="modal fade" id="enquiry-upload-modal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form id="enquiryUpload" enctype="multipart/form-data" action="{{url('user/excel-upload-enquiries-1')}}"
              method="POST">
            <input type="hidden" name="campaign_id" value="{{ request('campaign_id') ?? 0}}">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Upload Leads</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Upload File</label>
                            <input type="file" name="contacts" class="form-control" placeholder="" required>
                            <div class="row">
                                <div class="error contacts col-md-6">
                                    {{-- <span class="muted text-danger">* Required </span><br> --}}
                                </div>
                                <div class="col-md-6 text-right">
                                    <a href="{{url('/files/leads-sample.csv')}}"><i class="fa fa-download"></i> Sample
                                        File </a>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 import-info">
                                    <h6>Note : </h6>
                                    <p>1. <strong>Date Format Options:</strong> The date field should follow the
                                        <strong>yyyy-mm-dd</strong> format or <strong>dd-mm-yyyy</strong> format, as
                                        specified. (Example: 2021-12-31 or 31-12-2021)</p></strong>
                                    <p>2. <strong>Maximum Record Limit:</strong> Each file is limited to containing a
                                        maximum of <strong>5000</strong> records in a single process. </p>
                                    <p>3. <strong>Mandatory Fields:</strong></p>
                                    <ul class="pl-4">
                                        <li><strong>Country Code</strong></li>
                                        <li><strong>Mobile number</strong></li>
                                        <li><strong>Lead Source</strong></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <button class="btn btn-deep-orange demo-btn submitBtn" type="submit">Add</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="modal fade" id="name-add-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Assign to Agent - {{request('campaign_id')}}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body">
                    <form id="submitAssignAgent" method="POST" action="#">
                        {{csrf_field()}}
                        <div class="form-group">
                            <label for="recipient-name" class="form-control-label">Agent:</label>
                            <select class="form-control" name="staff_id" required>
                                <option value="">---- Select Agent ----</option>
                                <option value="-1">UnAssign Agent</option>
                                @if(!request('campaign_id'))
                                    @foreach($agents as $agent)
                                        <option value="{{$agent->pk_int_user_id}}">{{$agent->vchr_user_name}}</option>
                                    @endforeach
                                @endif
                            </select>
                        </div>
                        <input class="form-control col-md-8" type="hidden" name="id" id='lead-edit-id'>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary" id="button-edit">Submit</button>
                        </div>
                        <span class="error"></span>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- Connect Whatsapp Modal -->
    <div class="modal fade" id="connect-whatsapp-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Connect WhatsApp</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body">
            <span><i class="fa fa-warning"></i> Warning !!!<br/><br/>
            This is not an official whatsapp business service.Please use this feature carefully. <br/><br/>
            There may be chances of getting your whatsapp number blocked if you use it for bulk messaging. Getlead will not be responsible for any issues happens to your whatsapp account.<br/>
            <br/>
            For Official Whatsapp Apis please contact us on +************ <br/></span><br/>
                    <form id="submitWhatsappConnect" method="POST" action="#">
                        {{csrf_field()}}
                        <div class="form-group">
                            <label for="recipient-name" class="form-control-label">WhatsApp Number:</label>
                            <input type="number" type="number" name="number"
                                   @if($whatsapp_session) value="{{$whatsapp_session->number}}"
                                   @endif placeholder="WhatsApp Number Example.************" class="form-control"
                                   required>
                        </div>
                        <div class="form-group" id="qrcode"></div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-primary" id="button-connect-wh">Connect</button>
                        </div>
                        <span class="error"></span>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- End -->
    <div class="modal fade" id="ass_agent" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabelAgent"
         aria-hidden="true">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Assign to Agent</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body">
                    <form id="assignMultiple" method="POST" action="#">
                        {{csrf_field()}}
                        <div class="form-group">
                            <label for="recipient-name" class="form-control-label">Agent:</label>
                            <select class="form-control" name="staff_id" required>
                                <option value="">---- Select Agent ----</option>
                                <option value="-1">UnAssign Agent</option>
                                @if(!request('campaign_id'))
                                    @foreach($agents as $agent)
                                        <option value="{{$agent->pk_int_user_id}}">{{$agent->vchr_user_name}}</option>
                                    @endforeach
                                @endif
                            </select>
                        </div>
                        <input class="form-control col-md-8" type="hidden" name="lead_ids" id='lead_ids'>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-info" id="button-edit">Submit</button>
                        </div>
                        <span class="error"></span>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!--update status model -->
    <div class="modal fade" id="update_status" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabelAgent"
         aria-hidden="true">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Update Status</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body">
                    <form id="assignMultipleStatus" method="POST" action="#">
                        {{csrf_field()}}
                        <div class="form-group">
                            <label for="recipient-name" class="form-control-label">Status:</label>
                            <select class="form-control" name="status_id" id="status_id" required>
                                <option value="">---- Select Status ----</option>
                                @foreach($enquiry_status as $Status)
                                    <option value="{{$Status->pk_int_feedback_status_id }}">{{$Status->vchr_status}}</option>
                                @endforeach
                            </select>
                        </div>
                        <input class="form-control col-md-8" type="hidden" name="leadids" id='leadids'>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-info" id="button-edit">Submit</button>
                        </div>
                        <span class="error"></span>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!--end update status model-->
    <!--bulk update field model -->
    <div class="modal fade" id="bulk_update" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabelAgent"
         aria-hidden="true">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content">
                <form id="bulkUpdate" method="POST" action="#">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">Bulk Update</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        </button>
                    </div>
                    <div class="modal-body">
                        {{csrf_field()}}
                        <div class="form-group">
                            <label for="recipient-name" class="form-control-label">Status:</label>
                            <select class="form-control" name="bulk_status_id" id="bulk_status_id">
                                <option value="">---- No Change ----</option>
                                @foreach($enquiry_status as $Status)
                                    <option value="{{$Status->pk_int_feedback_status_id }}">{{$Status->vchr_status}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="recipient-name" class="form-control-label">Purpose:</label>
                            <select class="form-control" name="bulk_purpose_id" id="bulk_purpose_id">
                                <option value="">---- No Change ----</option>
                                @foreach($purposes as $purpose)
                                    <option value="{{$purpose->pk_int_purpose_id }}">{{$purpose->vchr_purpose}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="recipient-name" class="form-control-label">Lead Type:</label>
                            <select class="form-control" name="bulk_type_id" id="bulk_type_id">
                                <option value="">---- No Change ----</option>
                                @foreach($leadTypes as $lead_type)
                                    <option value="{{$lead_type->id}}">{{$lead_type->name}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="recipient-name" class="form-control-label">Source:</label>
                            <select class="form-control" name="bulk_source_id" id="bulk_source_id">
                                <option value="">---- No Change ----</option>
                                @foreach($types as $type)
                                    <option value="{{$type->pk_int_enquiry_type_id}}">{{$type->vchr_enquiry_type}}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="form-group">
                            @if($additionalRequiredDropdown)
                                @foreach($additionalRequiredDropdown as $add_field)
                                    <label for="recipient-name" class="form-control-label">{{$add_field->field_name}}
                                        :</label>
                                    <select class="form-control select2 add_flds_filt"
                                            style="width: 100%; height: 100%"
                                            name="add_fields[{{$add_field->id}}]"
                                            id="enquiryTypeSelectFilt">
                                        <option value="">
                                            Select {{$add_field->field_name}}
                                        </option>
                                        @foreach($add_field->values as $key=>$value)
                                            <option value="{{$value }}"
                                            >{{$value }}</option>
                                        @endforeach
                                    </select>
                                @endforeach
                            @endif
                        </div>
                    </div>
                    <input class="form-control col-md-8" type="hidden" name="bulk_leadids" id='bulk_leadids'>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-info" id="button-bulk-edit">Submit</button>
                    </div>
                    <span class="error"></span>
                </form>
            </div>
        </div>
    </div>
    </div>
    <!--end bulk update field model-->
    <div class="modal fade" id="action_modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabelAgent"
         aria-hidden="true">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Actions</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <i class="fa fa-close"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="col-sm-12">
                        <button class="btn btn-primary" type="button" id="btnCreateCallTask">
                            Create Call Task
                        </button>
                    </div>
                    <div class="col-sm-12">
                        <button class="btn btn-primary" type="button" id="btnCreateCampaign" data-toggle="modal"
                                data-target="#assign_campaign">
                            Add to Campaign
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="assign_campaign" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabelCampaign"
         aria-hidden="true">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Assign Campaign</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body">
                    <form id="assignCampaign" method="POST" action="#">
                        {{csrf_field()}}
                        <div class="form-group">
                            <label for="recipient-name" class="form-control-label">Campaign:</label>
                            <select class="form-control" name="campaign_id" id="campaign_id" required>
                                <option value="">---- Select Campaign ----</option>
                                @foreach($lead_campaigns as $lead_campaign)
                                    @if(request('unassigned'))
                                        <option value="{{$lead_campaign->id }}"
                                                {{request('campaign_id') == $lead_campaign->id ? 'selected' : ''}} readonly>{{$lead_campaign->name}}</option>
                                    @else
                                        <option value="{{$lead_campaign->id }}">{{$lead_campaign->name}}</option>
                                    @endif
                                @endforeach
                            </select>
                        </div>
                        <input class="form-control col-md-8" type="hidden" name="leads_ids" id="leads_ids">
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-info" id="assign-campaign">Submit</button>
                        </div>
                        <span class="error"></span>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade bd-example-modal-lg" id="custom_field_modal" tabindex="-1" role="dialog"
         aria-labelledby="myLargeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-centered" style="width:50%;min-width:50%">
            <div class="modal-content">
                <div class="modal-body contact-popup-body">
                    <h5 class="contact-popup-title text-left">Contacts</h5>
                    <hr class="pt-2 pb-2">
                    <div class="checkall" id="checkall">

                        {{-- @php
               $display_fields=[];
               if(auth()->user()->enquiry_display_fields)
               $display_fields = auth()->user()->enquiry_display_fields;
               $i=2;
               @endphp
               @foreach(\App\BackendModel\Enquiry::DISPLAY_FIELDS as $field)
               <div class="form-check">
                  <input class="form-check-input checkBoxClass custom_fields" data-val="{{$i}}" type="checkbox" @if(in_array($field['id'],$display_fields)) checked @endif value="{{$field['id']}}" id="flexCheckDefault">
                  <label class="form-check-label" for="flexCheckDefault">
                  {{$field['caption']}}
                  </label>
               </div>
               @php $i++; @endphp
               @endforeach   --}}
                        {{-- @foreach($additional_fields as $add_field)
               @if($add_field->show_in_list==1)
               <div class="form-check">
                  <input class="form-check-input checkBoxClass custom_fields" data-val="{{$i}}" type="checkbox" @if(in_array('a'.$add_field->id,$display_fields)) checked @endif value="a{{$add_field->id}}" id="flexCheckDefault">
                  <label class="form-check-label" for="flexCheckDefault">
                  {{$add_field->field_name}}
                  </label>
               </div>
               @php $i++; @endphp
               @endif
               @endforeach   --}}
                        {{-- <div class="clearfix"></div>   --}}
                    </div>
                    <div class="bottom-item mb-4">
                        <div class="bottom-left"
                             style="border-bottom: 1px  #ccc; @if(auth()->user()->int_role_id!=2) display:none; @endif">
                            <input type="checkbox" id="cf_all_users"> Apply for all users</input></div>
                        <div class="bottom-right" style="border-bottom: 1px dashed #ccc;"><a href="#" id="selectAll">Select
                                All</a></div>
                    </div>
                    <div class=" ">
                        <div class="contact-btn">
                            <a href="#" class="with-bg" onclick="applyCustomFields()">Apply</a><a href="#"
                                                                                                  class="with-border"
                                                                                                  data-dismiss="modal">Cancel</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!------------ /Add Leads to campaign/  -------------->
    <div class="modal fade" id="addLeads" tabindex="-1" role="dialog" aria-labelledby="myModalLabel"
         aria-hidden="true">
        <form id="enquiryUploadCampaign" enctype="multipart/form-data" action="{{url('user/add-leads-to-campaign')}}"
              method="POST">
            <input type="hidden" name="campaign_id" value="{{ request('campaign_id') ?? 0}}">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h4 class="modal-title w-100 font-weight-bold g-clr">Upload Leads</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body mx-3">
                        <div class="md-form mb-1">
                            <label for="default-input" class="form-control-label">Upload File</label>
                            <input type="file" name="contacts" class="form-control" placeholder="" required>
                            <div class="row">
                                <div class="error contacts col-md-6">
                                    <span class="muted text-danger">* Required </span><br>
                                </div>
                                <div class="col-md-6 text-right">
                                    <a href="{{url('/files/leads-sample-campaign.csv')}}"><i class="fa fa-download"></i>
                                        Sample
                                        File </a>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12">
                                    <span>Please make sure to keep mobile numbers in single row</span>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer d-flex justify-content-center">
                            <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            <button class="btn btn-deep-orange demo-btn" type="submit">Add</button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <!------- Create call task ---------------------->
    <div class="modal fade bd-example-modal-sm" id="create_call_task" tabindex="-1" role="dialog"
         aria-labelledby="myLargeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Create Call Task</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    </button>
                </div>
                <div class="modal-body">
                    <form id="createCallTask" method="POST" action="#">
                        {{csrf_field()}}
                        <div class="form-group" style="display: grid">
                            <label for="recipient-name" class="form-control-label">Assign To:</label>
                            <select class="form-control select2 callCrSelect" name="assign_by" id="assign_by" required>
                                <option value="1">Same as leads agent</option>
                                <option value="2">Other Agent</option>
                            </select>
                        </div>
                        <div class="form-group d-none" id="assignedAgents" style="display: grid">
                            <label for="recipient-name" class="form-control-label">Agents:</label>
                            <select class="form-control select2 callCrSelect" name="agent_id" id="agent_id">
                                <option value="">---- Select Agent ----</option>
                                @foreach($agents as $user)
                                    <option value="{{$user->pk_int_user_id }}">{{$user->vchr_user_name}}</option>
                                @endforeach
                            </select>
                        </div>
                        <input class="form-control col-md-8" type="hidden" name="leads_ids" id="leads_ids">
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            <button type="submit" class="btn btn-info" id="createTaskCallSubmit">Submit</button>
                        </div>
                        <span class="error"></span>
                    </form>
                </div>
            </div>
        </div>
    </div>

    @include('backend.pages.enquiries.next_follow_up')

@endsection
@push('footer.script')
    <style>
        /* .dataTables_scrollBody {
   transform: rotateX(180deg);
   overflow: visible !important;
   } */
        /* .dataTables_scrollHead {
   overflow: visible !important;
   } */
        /* .dataTables_scrollHeadInner{
   background:#fff !important;
   } */
        /* .dataTables_scrollBody table {
   transform: rotateX(180deg);
   } */
    </style>
    <style>
        #enquiry-info-table_paginate {
            display: block !important;
        }

        .DTFC_RightHeadWrapper, .DTFC_Cloned {
            background: #fff !important;
        }
    </style>


    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-cookie/1.4.1/jquery.cookie.min.js"
            integrity="sha512-3j3VU6WC5rPQB4Ld1jnLV7Kd5xr+cq9avvhwqzbH/taCRNURoeEpoPBK9pDyeukwSxwRPJ8fDgvYXd6SkaZ2TA=="
            crossorigin="anonymous"></script>
    <script src="//cdn.datatables.net/plug-ins/1.10.7/pagination/input.js"></script>
    <script src="https://cdn.datatables.net/fixedcolumns/3.3.3/js/dataTables.fixedColumns.min.js"></script>
    <script type="text/javascript" src="{{ url('backend/js/daterangepicker.js')}}"></script>
    <script src="{{url('js/bootstrap-datetimepicker.min.js')}}"></script>
    <script type="text/javascript">
        var country_code = {!! json_encode(config('constants.countries') )!!}
            var
        table = '';
        var from_date = '';
        var to_date = '';

        function clearfields() {
            $('#status_id').val("");
        }

        window.onload = dispalyContent();
        $("#next_follow_up_date").datepicker({minDate: new Date()});
        $("#datepicker2").datepicker({minDate: new Date()});

        function showFields(selected) {
            if (selected.value == 'Yes') {
                document.getElementById('div_date_of_purchase').style.display = "block";
                document.getElementById('div_remarks').style.display = "none";
            } else if (selected.value == 'No') {
                document.getElementById('div_date_of_purchase').style.display = "none";
                document.getElementById('div_remarks').style.display = "block";
            } else {
                document.getElementById('div_date_of_purchase').style.display = "none";
                document.getElementById('div_remarks').style.display = "none";
            }
        }

        function sortBy(type) {
            if ($('#sort_by').val() != type) {
                $('#sort_by').val(type);
                $('.sort_check').toggle();
                filterData();
            }
        }

        $(document).ready(function () {
            $("#enquiryUpload").submit(function () {
                $(".submitBtn").attr("disabled", true);
                return true;
            });
        });

        function searchForArray(haystack, needle) {
            var i;
            for (i = 0; i < haystack.length; ++i) {
                if (haystack[i] == needle) {
                    return 'checked';
                }

            }
            return '';
        }

        function dispalyContent() {
            $.ajax({
                url: '{{url("/user/displayContent")}}',
                type: 'POST',
                dataType: 'JSON',
                data: '',
                contentType: false,
                processData: false,
            }).done(function (res) {

                var enquiry_display_fields = res.enquiry_display_fields;
                var content = '';
                var i = 2;
                $.each(res.display_fields, function (index, val) {
                    var chk = '';
                    chk = (val.id==1)?'checked': searchForArray(enquiry_display_fields, val.id);
                    var hid = (val.id==1)?'style="display: none;':'';
                    content += '<div class="form-check"'+hid+'><input class="form-check-input checkBoxClass custom_fields" data-val="' + i + '" type="checkbox" ' + chk + '  value="' + val.id + '" id="flexCheckDefault"><label class="form-check-label" for="flexCheckDefault">' + val.caption + '</label></div>';
                    i = i + 1;
                });

                $.each(res.additional_fields, function (index, value) {
                    chk = '';
                    chk = searchForArray(enquiry_display_fields, 'a' + value.id)
                    if (value.show_in_list == 1) {
                        content += '<div class="form-check"><input class="form-check-input checkBoxClass custom_fields" data-val="' + i + '" type="checkbox"' + chk + ' value="a' + value.id + '" id="flexCheckDefault"><label class="form-check-label" for="flexCheckDefault">' + value.field_name + '</label></div>';
                        i = i + 1;
                    }

                });
                content += '<div class="clearfix"></div>';

                $('#checkall').html(content);
            }).fail(function () {

            }).always(function (com) {
                //    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
            });
        }

        // delete multiple leads
        function deleteMultiple() {
            if ($('#lead_ids').val() == '') {
                $.alert({
                    title: 'Error',
                    type: 'red',
                    content: 'Please choose leads',
                });
                return
            }

            $.confirm({
                title: 'Deletion',
                content: 'Are you sure you want to delete multiple enquires',
                icon: 'la la-question-circle',
                animation: 'scale',
                closeAnimation: 'scale',
                opacity: 0.5,
                buttons: {
                    'confirm': {
                        text: 'Proceed',
                        btnClass: 'btn-info',
                        action: function () {
                            $.ajax({
                                url: BASE_URL + '/user/delete-multiple-leads',
                                type: 'POST',
                                dataType: 'JSON',
                                data: new FormData(document.getElementById("delete_multiple_lead")),
                                contentType: false,
                                processData: false,
                            }).done(function (res) {
                                if (res.status == 'success') {
                                    $.alert({
                                        title: 'Success',
                                        type: 'green',
                                        content: res.msg,
                                    });
                                    $('.checkAll').prop('checked', false);
                                    // $('#delete_leads').hide();
                                    // $('#btnActions').hide();
                                } else {
                                    $.alert({
                                        title: 'Error',
                                        type: 'red',
                                        content: res.msg,
                                    });
                                    $('#checkAll').prop('checked', false);
                                    // $('#delete_leads').hide();
                                    // $('#btnActions').hide();
                                }
                            }).fail(function () {

                            }).always(function (com) {
                                $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                            });
                        }
                    },
                    cancel: function () {
                        $.alert('Operation <strong>canceled</strong>');
                    }
                }
            });
        }

        function campaignTaskRemove() {
            $.ajax({
                url: BASE_URL + '/user/campaign-task-remove{{( request("campaign_id") ? "?campaign_id=".request("campaign_id") : "" )}}',
                type: 'POST',
                dataType: 'JSON',
                data: new FormData(document.getElementById("delete_multiple_lead")),
                contentType: false,
                processData: false,
            }).done(function (res) {
                if (res.status == 'success') {
                    $.alert({
                        title: 'Success',
                        type: 'green',
                        content: res.msg,
                    });
                    $('.checkAll').prop('checked', false);
                } else {
                    $.alert({
                        title: 'Error',
                        type: 'red',
                        content: res.msg,
                    });
                    $('#checkAll').prop('checked', false);
                }
            }).fail(function () {

            }).always(function (com) {
                $('#enquiry-info-table').DataTable().ajax.reload(null, false);
            });
        }

        // ends
        //Set Custom fields
        function applyCustomFields() {
            var selected_fields = [];
            $('.custom_fields').each(function () {
                try {
                    var column = table.column($(this).attr('data-val'));
                    if ($(this).is(':checked')) {
                        selected_fields.push($(this).val());
                        column.visible(true);
                    } else {
                        column.visible(false);
                    }
                } catch (exp) {

                }
            });
            $('#custom_field_modal').modal('hide')
            $.ajax({
                url: '{{url("user/set-custom-fields")}}',
                type: 'POST',
                data: {
                    fields: selected_fields,
                    apply_for_all: $('#cf_all_users').is(':checked') ? 1 : 0
                }
            }).done(function (res) {

            }).fail(function () {

            }).always(function (com) {
            });
        }

        //
        $(document).ready(function () {


            $('#sort_by').change(function () {
                filterData();
            });

            $('#enquiry-info-table').on('click', '.form-check-assign', function (event) {
                var arr = [];
                $('.form-check-assign input[type="checkbox"]:checked').each(function () {
                    arr.push($(this).attr('data-enq_id'));
                });
                $('#leadids').val(arr);
                $('#bulk_leadids').val(arr);
            });

            $(document).on('submit', '#assignMultipleStatus', function (event) {
                event.preventDefault();
                $.ajax({
                    url: '{{url('user/multiple-enq-status-update')}}',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    $('#update_status').modal('hide');

                    if (res.status == true) {
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                        $('#checkAll').prop('checked', false);
                    } else {
                        $.alert({
                            title: 'Validation Error',
                            type: 'red',
                            content: res.msg,
                        });
                        $('#checkAll').prop('checked', true);
                    }


                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                }).fail(function () {

                }).always(function (com) {
                    //  $('#tasks_data_table').DataTable().ajax.reload(null, false);
                });

            });
            //Bulk Update
            $(document).on('submit', '#bulkUpdate', function (event) {
                event.preventDefault();
                $('#button-bulk-edit').html('Please wait...')
                $('#button-bulk-edit').prop('disabled', true)
                $.ajax({
                    url: '{{url('user/multiple-enq-bulk-update')}}',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    $('#bulk_update').modal('hide');

                    if (res.status == true) {
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                        $('#checkAll').prop('checked', false);
                    } else {
                        $.alert({
                            title: 'Validation Error',
                            type: 'red',
                            content: res.msg,
                        });
                        $('#checkAll').prop('checked', true);
                    }


                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                }).fail(function () {
                    alert("Under maintanance")
                }).always(function (com) {
                    $('#button-bulk-edit').html('Submit')

                    $('#button-bulk-edit').prop('disabled', false)
                    //  $('#tasks_data_table').DataTable().ajax.reload(null, false);
                });

            });

            var start = moment().startOf('month');
            var end = moment().endOf('month');
            @if(\App\User::getVendorId() == 1344)
                from_date = moment().startOf('month').format('Y-MM-DD');
            to_date = moment().endOf('month').format('Y-MM-DD');
            @endif
                switch (@json(request('index'))) {
                case 'today' :
                    var start = moment();
                    var end = moment();
                    from_date = start.format('Y-MM-DD');
                    to_date = end.format('Y-MM-DD');
                    break;
                case 'week' :
                    var start = moment().startOf('week');
                    var end = moment();
                    from_date = start.format('Y-MM-DD');
                    to_date = end.format('Y-MM-DD');
                    break;
                case 'month' :
                    var start = moment().startOf('month');
                    var end = moment().endOf('month');
                    from_date = start.format('Y-MM-DD');
                    to_date = end.format('Y-MM-DD');
                    break;
                case 'total' :
                    var start = moment('2015-01-01');
                    var end = moment();
                    from_date = start.format('Y-MM-DD');
                    to_date = end.format('Y-MM-DD');
                    break;
                case '0'      :
                    var start = moment('2015-01-01');
                    var end = moment();
                    from_date = start.format('Y-MM-DD');
                    to_date = end.format('Y-MM-DD');
                    break;
                case 'default':
                    var start = moment().startOf('month');
                    var end = moment();
                    from_date = start.format('Y-MM-DD');
                    to_date = end.format('Y-MM-DD');
                    break;
            }


            function cb(start, end) {
                $('#all_date span').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
            }

            $('#all_date').daterangepicker({
                startDate: start,
                endDate: end,
                locale: {
                    format: 'YYYY-MM-DD'
                },
                ranges: {
                    'All time': [moment('2015-01-01'), moment()],
                    'Today': [moment(), moment()],
                    'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                    'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                    'This Month': [moment().startOf('month'), moment().endOf('month')],
                    'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
                }
            }, cb);

            // cb(start, end);

        });


        @if(Auth::user()->int_role_id==2)
        function WhatsappModal() {
            $('#qrcode').html('')
            $('#connect-whatsapp-modal').modal('show');
        }

        @endif
        function showCompetingModel(selected) {
            if (selected.value == 'Yes') {
                document.getElementById('div_competing_model').style.display = "block";
            } else if (selected.value == 'No') {
                document.getElementById('div_competing_model').style.display = "none";
            } else {
                document.getElementById('div_competing_model').style.display = "none";
            }
        }

        // function to set am and pm
        function onTimeChange() {
            var inputEle = document.getElementById('next_follow_up_time');
            var timeSplit = inputEle.value.split(':'),
                hours,
                minutes,
                meridian;
            hours = timeSplit[0];
            minutes = timeSplit[1];
            if (hours > 12) {
                meridian = 'PM';
                hours -= 12;
            } else if (hours < 12) {
                meridian = 'AM';
                if (hours == 0) {
                    hours = 12;
                }
            } else {
                meridian = 'PM';
            }
            $('#next_follow_up_time_value').val(hours + ':' + minutes + meridian);
        }

        function onDateChange() {

            var inputEle = document.getElementById('next_follow_up_date');
            var dateSplit = inputEle.value.split('-');
            var date = dateSplit[2];
            var month = dateSplit[1];
            var year = dateSplit[0];
            $('#next_follow_up_date_value').val(month + '/' + date + '/' + year);
            fetchFollowupCount(inputEle.value);
        }

        // checking all check box in the data table
        $("#checkAll").click(function () {
            var checked = $(this).prop('checked');

            $('#enquiry-info-table').find('input:checkbox').prop('checked', checked);

            var arr = [];
            $('.form-check-assign input[type="checkbox"]:checked').each(function () {
                arr.push($(this).attr('data-enq_id'));
            });
            $('#lead_ids').val(arr);
            $('#leadids').val(arr);
            $('#bulk_leadids').val(arr);
            $('#id_leads').val(arr);
            $('#leads_ids').val(arr);
        });
        // ends

        $(function () {
            $('.date').datepicker({
                dateFormat: 'yy-mm-dd',
                autoclose: true,
            });
        });

        $(document).ready(function () {
            onDateChange();
            showLoaderFilter()
            $('.summernote-1').summernote({
                height: '215',
                toolbar: [
                    ['style', ['bold', 'italic', 'underline', 'clear']],
                    ['font', ['strikethrough', 'superscript', 'subscript']],
                    ['fontsize', ['fontsize']],
                    ['color', ['color']],
                    ['para', ['ul', 'ol', 'paragraph']],
                    ['height', ['height']]
                ]
            });
            $('#next_follow_up_spin').hide();
            $('#enquiry-info-table').on('click', '.form-check-assign', function (event) {
                var arr = [];
                $('.form-check-assign input[type="checkbox"]:checked').each(function () {
                    arr.push($(this).attr('data-enq_id'));
                });
                $('#lead_ids').val(arr);
                $('#id_leads').val(arr);
                $('#leads_ids').val(arr);
            });


            var filterVal = getUrlParameter('filter');
            var statusFil = getUrlParameter('status_filter');
            $('.select2').select2();
            width: 'resolve'

            BASE_URL = {!! json_encode(url('/')) !!}
            $(document).on('submit', '#enquirySubmit', function (event) {
                event.preventDefault();
                $('.error').hide();
                $.ajax({
                    url: BASE_URL + '/user/enquiries',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData,
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status == 'success') {
                        $('#ks-izi-modal-large').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });

                    } else {
                        $.each(res.msg, function (index, val) {
                            //iterate through array or object
                            $('.' + index).html(val);
                            $('.' + index).show();
                        });
                    }
                }).fail(function () {

                }).always(function (com) {
                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                });
            });

            $(document).on('change', '#assign_by', function ($q) {
                switch ($(this).val()) {
                    case '1':
                        $('#assignedAgents').addClass('d-none');
                        break;
                    case '2':
                        $('#assignedAgents').removeClass('d-none');
                        break;
                    default:
                        break;
                }
            })
            //create multiple tasks
            $(document).on('click', '#btnCreateCallTask', function (event) {
                event.preventDefault();
                if ($('#leads_ids').val() == '') {
                    $.alert({
                        title: 'Error',
                        type: 'red',
                        content: 'Please choose leads!!',
                    });
                    return false;
                }
                $('#create_call_task').modal('show');
            });

            $(document).on('submit', '#createCallTask', function (event) {
                if ($('#leads_ids').val() == '') {
                    $.alert({
                        title: 'Error',
                        type: 'red',
                        content: 'Please choose leads!!',
                    });
                    return false;
                }
                event.preventDefault();
                var formData = new FormData(document.getElementById("createCallTask"));
                formData.append('leads_ids_call_task', $('#leads_ids').val());
                $.confirm({
                    title: 'Create Call Task Confirmation',
                    content: 'Are you sure you want to create  call tasks?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: BASE_URL + '/user/create-call-multiple-leads',
                                    type: 'POST',
                                    dataType: 'JSON',
                                    data: formData,
                                    contentType: false,
                                    processData: false,
                                }).done(function (res) {
                                    if(res.msg=="Already have a task")
                                    {
                                        $.alert({
                                            title: 'Error',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                        $('#checkAll').prop('checked', false);
                                    }
                                    else if (res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                        $('.checkAll').prop('checked', false);
                                    } else {
                                        $.alert({
                                            title: 'Error',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                        $('#checkAll').prop('checked', false);
                                    }
                                }).fail(function () {

                                }).always(function (com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                    $('#create_call_task').modal('toggle');
                                    $("#createCallTask")[0].reset();
                                    $(".callCrSelect").select2();
                                    $('.callCrSelect').select2().trigger('change');
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });

            });

            //
            $(document).on('submit', '#enquirySubmitEdit', function (event) {
                event.preventDefault();
                $('.error').html('');
                $('.error').hide();
                var more_phone_number = [];
                var id = $('#pk_int_enquiry_id').val();
                $.ajax({
                    url: BASE_URL + '/user/enquiries/' + id,
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status == 'success') {
                        $('#ks-izi-modal-large1').modal('toggle');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                        try {
                            var iframe = document.getElementById('timelineIframe');
                            iframe.src = iframe.src;
                        } catch (exp) {

                        }
                    } else {

                        $.alert({
                            title: 'Error',
                            type: 'red',
                            content: res.msg,
                        });
                    }
                }).fail(function () {

                }).always(function (com) {
                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                });

            });

            table = $('#enquiry-info-table').DataTable({
                scrollX: true,
                paging: true,
                serverSide: true,
                language: {
                    searchPlaceholder: 'Search',
                    sSearch: '',
                    lengthMenu: '_MENU_ page',
                },
                "lengthMenu": [
                    [10, 25, 50, 100, 150, 200, 250, 500],//, 99999999999],
                    [10, 25, 50, 100, 150, 200, 250, 500]//, 'All']
                ],
                //  "pageLength": 5,
                columnDefs: [
                    {"width": "60px", "targets": 3},
                    {"width": "40px", "targets": 2}
                ],

                ajax: {
                    'type': 'POST',
                    "dataSrc": "data.data",
                    'url': BASE_URL + '/user/get-enquiries{{ request("follow_up_leads") ? "?follow_up_leads=yes" : ( request("yet_to_contacts") ? "?yet_to_contacts=yes" : ( request("campaign_id") ? "?campaign_id=".request("campaign_id") : "" ) ) }}',
                    "data": function (d) {
                        d.enquiry_type = $('#enquiryTypeId').val();
                        d.enquiry_purpose = $('#enquiryPurposeId').val();
                        d.enquiry_status = $('#enquiryStatusId').val();
                        d.created_by = $('#created_by').val();
                        d.created_at_from = from_date;
                        d.created_at_to = to_date;
                        d.staff_id = $('#staff_id').val();
                        d.lead_type_id = $('#leadTypeId').val();
                        d.district_id = $('#districtId').val();
                        d.followup_added = $('#followup_added').val();
                        //    d.assigned_date = $('#assigned_date').val();
                        d.sortby = $('#sort_by').val();
                        d.dateBy = $('#dateBy').val();
                        d.campaign_id = $('#campaign_id').val();
                        //    d.campaign_type =  {{request()->has('campaign_type') ?request('campaign_type') : 'false'}};
                        d.unassigned = {{request()->has('unassigned') ? 'true' : 'false'}};
                        d.attention = {{request()->has('attention') ? 'true' : 'false'}};
                        var ar = [];
                        var ids = [];
                        $('.add_flds').map(function () {
                            if (this.value != "") {
                                ar.push($(this).val());
                                ids.push($(this).attr('name'));
                            }
                        }).get();
                        d.additional_ids = ids;
                        d.additional_val = ar;
                        d.filter = filterVal;
                        d.status_filter = statusFil;
                        d.branch_id = $('#branch_id').val();
                        d.did_filter = $('#did_filter').val();
                        @if(request('campaign_id'))
                            d.campaign_status = $('#campaign_status').val();
                        @endif
                    }
                },
                "fnRowCallback": function (nRow, aData, iDisplayIndex, iDisplayIndexFull) {
                    $('td', nRow);
                    //    .css('color', aData.vchr_color)
                },
                columns: [
                    {data: 'assign_agent', name: 'assign_agent', orderable: false, searchable: false},
                    // {data: 'slno', name: 'slno'},
                    {data: 'show', name: 'show', orderable: false, searchable: false},
                    {data: 'cust_name', name: 'cust_name'},
                    {data: 'mobno', name: 'mobno'},
                    {data: 'assigned_to', name: 'assigned_to'},
                    {data: 'enq_type', name: 'enq_type'},
                    {data: 'lead_type', name: 'lead_type'},
                    {data: 'feedback', name: 'feedback'},
                    {data: 'source', name: 'source'},
                    {data: 'vchr_customer_email', name: 'vchr_customer_email'},
                    {data: 'address', name: 'address'},
                    {data: 'mobile', name: 'mobile'},
                    {data: 'created_date', name: 'created_date'},
                    {data: 'updated_date', name: 'updated_date'},
                    {data: 'created', name: 'created'},
                    {data: 'next_follow_up_data', name: 'next_follow_up_data'},
                    {data: 'assigned_date', name: 'assigned_date'}
                    @foreach($additional_fields as $add_field)
                    @if($add_field->show_in_list==1)
                    , {data: 'additional_{{$add_field->id}}', name: 'additional_{{$add_field->id}}'}
                    @endif
                    @endforeach
                ],
                "initComplete": function (settings, json) {
                    var pagenumber = $.cookie("leads_page_of_user_{{\Auth::id()}}");
                    if (typeof pagenumber != 'undefined') {
                        $('.paginate_input').val(pagenumber);
                        $('.paginate_input').keyup();
                    }

                    @if(auth()->user()->enquiry_display_fields)
                    $('.custom_fields').each(function () {
                        try {
                            var column = table.column($(this).attr('data-val'));
                            if ($(this).is(':checked')) {
                                column.visible(true);
                            } else {

                                column.visible(false);
                            }
                        } catch (exp) {

                        }
                    });
                    @else
                    $('.custom_fields').each(function () {
                        try {
                            var column = table.column(11);
                            column.visible(false);

                        } catch (exp) {

                        }
                    });
                    @endif
                    hideLoaderFilter()
                },
                "drawCallback": function (settings) {
                    // Hide the custom loader after each draw event (when data is updated)
                    hideLoaderFilter()
                },

            });

            $('.paginate_input').on('change', function () {
                // var info = table.page.info();
                var date = new Date();
                var minutes = 10;
                date.setTime(date.getTime() + (minutes * 60 * 1000));
                $.cookie("leads_page_of_user_{{\Auth::id()}}", $(this).val(), {expires: date});
            });

            // get competing models based on model selected
            $('.model_id').on('change', function () {
                event.preventDefault();
                $.ajax({
                    url: BASE_URL + '/user/model/competing-model/' + document.getElementById('model_id').value,
                    type: 'GET',
                    dataType: 'JSON',
                    contentType: false,
                    processData: false,
                })
                    .done(function (res) {
                        if (res.status == 'success') {
                            options = "";
                            $("#competing_model").val();
                            for (var i = 0; i < res.data.length; i++) {
                                options += '<option>-- Select Competing Model --</soption><option value="' + res.data[i]['id'] + '">' + res.data[i]['name'] + '</option>'
                            }
                            $('#competing_model').html(options);
                        } else {
                            alert('No data found on selected gateway')
                        }
                    })
                    .fail(function () {
                    })
                    .always(function (com) {
                        $('#enquiry-table').DataTable().ajax.reload(null, false);
                    });
            });
            //ends
            $('#enquiry-info-table').on('click', '.ks-izi-modal-trigger1', function (event) {
                enquiryId = $(this).attr('enquiry-id');
                editEnquiry(enquiryId);
            });

            $('#enquiry-info-table').on('click', '.ks-izi-modal-trigger2', function (event) {
                $('#detail-body').html('<iframe id="timelineIframe" style="width:100%;height:' + (window.innerHeight - 150) + 'px;border:none" src="' + BASE_URL + '/user/enquiry-timeline/' +  $(this).attr('enquiry-id') + '">Your browser is not compatible</iframe>');
            });

            $('.district_id').on('change', function () {
                event.preventDefault();
                $.ajax({
                    url: BASE_URL + '/user/get-taluk/' + document.getElementById('district_id').value,
                    type: 'GET',
                    dataType: 'JSON',
                    contentType: false,
                    processData: false,
                })
                    .done(function (res) {
                        if (res.status == 'success') {
                            options = "";
                            $("#taluk_id").val();
                            for (var i = 0; i < res.data.length; i++) {
                                options += '<option value="' + res.data[i]['id'] + '">' + res.data[i]['name'] + '</option>'
                            }
                            $('#taluk_id').html(options);
                        } else {
                            alert('No data found on selected gateway')
                        }
                    })
                    .fail(function () {
                    })
                    .always(function (com) {
                        $('#enquiry-table').DataTable().ajax.reload(null, false);
                    });
            });

            // function to get selected competing models based on model selected
            function getCompetingModels(model_id, competing_model_id) {
                event.preventDefault();
                $.ajax({
                    url: BASE_URL + '/user/model/competing-model/' + model_id,
                    type: 'GET',
                    dataType: 'JSON',
                    contentType: false,
                    processData: false,
                })
                    .done(function (res) {
                        if (res.status == 'success') {
                            options = "";
                            for (var i = 0; i < res.data.length; i++) {
                                if (competing_model_id == res.data[i]['id']) {
                                    $selected = 'selected';
                                } else {
                                    $selected = '';
                                }
                                options += '<option>-- Select Competing Model --</soption><option value="' + res.data[i]['id'] + '"' + $selected + '>' + res.data[i]['name'] + '</option>';
                            }
                            document.getElementById("competing_model").innerHTML = options;
                        } else {
                            alert('No data found on selected model')
                        }
                    })
                    .fail(function () {
                    })
            }

            // ends

            // ends
            $('#enquiry-info-table').on('click', '.enquiry-act', function (event) {
                event.preventDefault();
                if ($(this).hasClass('btn-activate')) {
                    var url = BASE_URL + '/admin/enquiry-deactivate/';
                    action = 'Deactivate';
                } else {
                    var url = BASE_URL + '/admin/enquiry-activate/';
                    action = 'Activate';
                }
                var enquiryId = $(this).attr('enquiry-id');
                url = url + enquiryId;
                $.confirm({
                    title: action,
                    content: 'Are you sure you want to ' + action + ' ?',
                    icon: 'la la-question-circle',
                    animation: 'scale',
                    closeAnimation: 'scale',
                    opacity: 0.5,
                    buttons: {
                        'confirm': {
                            text: 'Proceed',
                            btnClass: 'btn-info',
                            action: function () {
                                $.ajax({
                                    url: url,
                                    type: 'GET',
                                }).done(function (res) {
                                    if (res.status == 'success') {
                                        $.alert({
                                            title: 'Success',
                                            type: 'green',
                                            content: res.msg,
                                        });
                                    } else {
                                        $.alert({
                                            title: 'Failed',
                                            type: 'red',
                                            content: res.msg,
                                        });
                                    }
                                }).fail(function (err) {

                                }).always(function (com) {
                                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                                });
                            }
                        },
                        cancel: function () {
                            $.alert('Operation <strong>canceled</strong>');
                        }
                    }
                });
            });

            $('#enquiry-info-table').on('click', '.enquiry-delete', function (event) {
                event.preventDefault();
                var enquiryId = $(this).attr('enquiry-id');
                deleteEnquiry(enquiryId);
            });
            // More Phone Numbers

            $("#add_phone").click(function () {
                $("#row_add_phone").append(
                    '<div class="row m-0"><select name="more_country_code[]" id="" class="form-control col-md-3 select2" >' + country_code + '</select>&nbsp;<input type="text" name="more_phone_numbers[]" class="form-control col-md-7 " placeholder="Number with Country code" onkeypress="return /[0-9]/i.test(event.key)"><a href="#" class="remove_field  ml-1 mt-1"><i class="fa fa-minus-circle text-danger m-1" style="font-size:14px;"></i></a></div>'
                );
            });

            $(document).on("click", ".remove_field", function () {
                $(this).closest(".row").remove();
            });

            $("#add_more_number").click(function () {
                $("#more_numbers_a").append(
                    '<div class="row m-0" id="add_more_numb"><select name="more_country_code[]" id="" class="form-control col-md-3 select2" >' + country_code + '</select>&nbsp;<input type="text" name="more_phone_numbers[]" class="form-control col-md-7 " placeholder="Number with Country code" onkeypress="return /[0-9]/i.test(event.key)"> <a href="#" class="remove_field  ml-1 mt-1"> <i class="fa fa-minus-circle " style="font-size:14px;color:red"></i></a></div>'
                );

            });


            $("#selectAll").click(function () {
                $(".checkBoxClass").prop('checked', true);
            });

            $(document).on('submit', '#submitAssignAgent', function (event) {
                event.preventDefault();
                $.ajax({
                    url: '{{url('user/assigned-to-agent')}}',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    if (res.status) {
                        $('#name-add-modal').modal('hide');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                        $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                    } else
                        $.alert({
                            title: 'Failed',
                            type: 'red',
                            content: res.msg,
                        });
                }).fail(function () {

                }).always(function (com) {
                    $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                });

            });
            @if(Auth::user()->int_role_id==2)
            $(document).on('submit', '#submitWhatsappConnect', function (event) {
                event.preventDefault();
                $('#button-connect-wh').prop('disabled', true)
                $('#qrcode').html('Please wait...')
                $.ajax({
                    url: "{{url('user/generate-whatsapp-qr')}}",
                    type: 'POST',
                    data: $('#submitWhatsappConnect').serialize(),
                }).done(function (res) {
                    $('#qrcode').html(res);
                }).fail(function () {

                }).always(function (res) {
                    $('#button-connect-wh').prop('disabled', false)
                    $('#button-connect-wh').text('Connect')
                });

            });
            @endif
            //   task add
            $(document).on('submit', '#AddNextFollowUp', function (event) {
                event.preventDefault();
                $('#next_follow_up_spin').show();
                var taskname = $('#task-name').val();
                var datevalue = $('#next_follow_up_date_value').val();
                var timevalue = $('#next_follow_up_time_value').val();
                var tasktype = $('#task-type').val();
                var assigned = $('#assigned-to').val();
                var note = $('#next_follow_up_note').val();
                if (taskname == "") {
                    $('.task-name').text('* Required Field')

                } else if (datevalue == "") {
                    $('.next_follow_up_date').text('* Required Field')
                } else if (timevalue == "") {
                    $('.next_follow_up_time').text('* Required Field')
                } else if (note == "") {
                    $('.next_follow_up_note').text('* Required Field')

                } else if (tasktype == "") {
                    $('.task-type').text('* Required Field')

                } else if (taskname != '' && datevalue != '' && timevalue != '' && tasktype != '' && note != '')// && assigned != '')
                    $.ajax({
                        url: '{{url('user/add-timeline-task')}}',
                        type: 'POST',
                        dataType: 'JSON',
                        data: new FormData(this),
                        contentType: false,
                        processData: false,
                    }).done(function (res) {
                        $("#AddNextFollowUp")[0].reset();
                        $('#next_follow_up_spin').hide();
                        $('#next_follow_up').modal('hide');
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: "A task scheduled successfully",
                        });


                    }).fail(function () {

                        $("#AddNextFollowUp")[0].reset();
                        $('#next_follow_up_spin').hide();
                        $('#next_follow_up').modal('hide');
                    }).always(function (com) {
                        $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                    });

            });

            //end
            $(document).on('submit', '#assignMultiple', function (event) {
                event.preventDefault();
                $.ajax({
                    url: '{{url('user/assigned-to-agent-multiple-enq')}}',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {

                    if (res.status == true) {
                        $('#ass_agent').modal('hide');

                        $.alert({
                            title: 'Successfully Asigned',
                            type: 'green',
                            content: res.msg,
                        });
                        // $('#checkAll').prop('checked', false);
                    } else {

                        $.alert({
                            title: 'Failed to Assign',
                            type: 'red',
                            content: res.msg,
                        });
                        // $('#checkAll').prop('checked', true);
                    }


                    $('#enquiry-info-table').DataTable().ajax.reload(function () {

                        var selected_ids = $('#lead_ids').val().split(',');
                        $('.form-check-assign input[type="checkbox"]').each(function () {

                            if (selected_ids.includes($(this).attr('data-enq_id'))) {
                                $(this).prop('checked', true);
                            }
                        });
                    }, false);
                }).fail(function () {
                    $('#ass_agent').modal('hide');
                }).always(function (com) {
                    //  $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                });

            });

            $(document).on('submit', '#assignCampaign', function (event) {
                if ($('#leads_ids').val() == '') {
                    $.alert({
                        title: 'Error',
                        type: 'red',
                        content: 'Please choose leads!!',
                    });
                    return false;
                }
                event.preventDefault();
                $.ajax({
                    url: '{{url('user/assign-lead-to-a-campaign')}}',
                    type: 'POST',
                    dataType: 'JSON',
                    data: new FormData(this),
                    contentType: false,
                    processData: false,
                }).done(function (res) {
                    $('#assign_campaign').modal('hide');
                    $('#action_modal').modal('hide');
                    if (res.status == true) {
                        $.alert({
                            title: 'Success',
                            type: 'green',
                            content: res.msg,
                        });
                        $('#checkAll').prop('checked', false);
                    } else {
                        $.alert({
                            title: 'Validation Error',
                            type: 'red',
                            content: res.msg,
                        });
                        $('#checkAll').prop('checked', true);
                    }


                    // $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                }).fail(function () {

                }).always(function (com) {
                    //  $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                });

            });
            //Validate mobile number
            $('.customer_mobile').on('change', function () {
                $.ajax({
                    url: BASE_URL + '/user/validate-leads-mobile',
                    type: 'POST',
                    dataType: 'JSON',
                    data: {
                        mobile: $(this).val(),
                        id: $('#pk_int_enquiry_id').val()
                    }
                })
                    .done(function (res) {
                        if (res == '1') {
                            alert('Mobile Number Already Exist');
                        }
                    })
                    .fail(function () {
                    });
            });
            //

        });

        /*Get URL Pramater */
        var getUrlParameter = function getUrlParameter(sParam) {
            var sPageURL = window.location.search.substring(1),
                sURLVariables = sPageURL.split('&'),
                sParameterName,
                i;
            for (i = 0; i < sURLVariables.length; i++) {
                sParameterName = sURLVariables[i].split('=');
                if (sParameterName[0] === sParam) {
                    return sParameterName[1] === undefined ? true : decodeURIComponent(sParameterName[1]);
                }
            }
        };

        function filterData() {
            showLoaderFilter()
            if ($('#all_date').val() != '') {
                var all_date = $('#all_date').val().split(' - ');
                from_date = all_date[0];
                to_date = all_date[1];
            }
            $('#enquiry-info-table').DataTable().ajax.reload();
        }

        function showEditModal(id) {
            $('.error').html('');
            $('#lead-edit-id').val(id);
            $('#name-add-modal').modal('show');
        }

        function showNextFollowUpModal(id) {
            $('#date_time_sec').show();
            $('#note_sec').show();
            $('.error').html('');
            $('#lead_id').val(id);
            $('#next_follow_up').modal('show');
        }

        function changeFollowUpField(val) {
            if ($('#follow_up').val() == 1) {

                $('#date_time_sec').show();
                $('#note_sec').show();
            } else {
                $('#date_time_sec').hide();
                $('#note_sec').hide();
            }
        }

        // function to get taluks based on district selected
        function getTaluks(district_id, taluk_id) {
            event.preventDefault();
            $.ajax({
                url: BASE_URL + '/user/get-taluk/' + district_id,
                type: 'GET',
                dataType: 'JSON',
                contentType: false,
                processData: false,
            })
                .done(function (res) {
                    if (res.status == 'success') {
                        options = "";
                        for (var i = 0; i < res.data.length; i++) {
                            if (taluk_id == res.data[i]['id']) {
                                $selected = 'selected';
                            } else {
                                $selected = '';
                            }
                            options += '<option value="' + res.data[i]['id'] + '"' + $selected + '>' + res.data[i]['name'] + '</option>'; //Create one textbox as HTML
                        }
                        document.getElementById("taluk_id").innerHTML = options;
                    } else {
                        alert('No data found on selected gateway')
                    }
                })
                .fail(function () {
                })

        }

        function editEnquiry(enquiryId) {
            $.ajax({
                url: BASE_URL + '/user/enquiries/' + enquiryId,
                type: 'GET',
                dataType: 'JSON',
            }).done(function (res) {
                $('#more_numbers_a').html("");
                if (res.status === "success") {
                    data = res.data;
                    $("[name='pk_int_enquiry_id']").val(data.pk_int_enquiry_id);
                    enquiryId = data.pk_int_enquiry_id;
                    if ($('#campaign_id').val() != 0) {
                        $("[name='staff_id']").val(data.staff_id).attr("disabled", true);
                    } else {
                        $("[name='staff_id']").val(data.staff_id);
                    }
                    $("[name='agency_id']").val(data.agency_id);
                    $("[name='district_id']").val(data.district_id);
                    enquiryId = data.pk_int_enquiry_id;
                    $("[name='vchr_customer_name']").val(data.vchr_customer_name);
                    $("[name='vchr_customer_company_name']").val(data.vchr_customer_company_name);
                    $("[name='vchr_customer_email']").val(data.vchr_customer_email);
                    $("[name='vchr_customer_mobile']").val(data.mobile_no);
                    $("[name='country_code']").val(data.country_code);
                    $("[name='landline_number']").val(data.landline_number);
                    $("[name='purchase_plan']").val(data.purchase_plan);
                    $("[name='live_deal']").val(data.live_deal);
                    $("[name='model_id']").val(data.model_id);

                    if (data.purchase_plan == 'Yes') {
                        document.getElementById('div_date_of_purchase').style.display = "block";
                        document.getElementById('div_remarks').style.display = "none";
                        $("[name='date_of_purchase']").val(data.date_of_purchase);
                    } else if (data.purchase_plan == 'No') {
                        document.getElementById('div_date_of_purchase').style.display = "none";
                        document.getElementById('div_remarks').style.display = "block";
                        $("[name='remarks']").val(data.remarks);
                    }

                    if (data.live_deal == 'Yes') {
                        document.getElementById('div_competing_model').style.display = "block";
                        getCompetingModels(data.model_id, data.competing_model)

                    } else if (data.live_deal == 'No') {

                    }

                    $.each(data.more_phone_numbers, function (key, value) {

                        var content = '<div class="row m-0">' + '<input type="hidden" name="more_country_code[' + key + ']" value="">' +
                            '<input type="text" class="form-control col-md-11"  name="more_phone_numbers[' +
                            key + ']" required multiple="" value="' + value +
                            '" placeholder="Number with Country code" onkeypress="return /[0-9]/i.test(event.key)"/>' +
                            '<a href="#" class="remove_field ml-1 mt-0 ">' +
                            '<i class="fa fa-minus-circle" style="font-size:14px;color:red">' +
                            '</i>' + '</a>' +
                            '</div>';
                        $('#more_numbers_a').append(content);
                        $('#more_numbers_a').on("click", ".remove_field", function () {
                            $(this).closest(".row").remove();
                        });
                    });
                    $('#add_details').html('');
                    var add_content = '';
                    $.each(data.additional_details, function (key, value) {
                        var status = (value.additional_purpose_count > 0) ? 'd-none' : '';
                        if (value.input_type == 1) {
                            if (value.is_required == 1) {
                                add_content = add_content + '<div class="md-form mb-1 col-md-6 ' + status + ' prps_' + value.id + '">' +
                                    '<label>' + value.field_name + '</label>' +
                                    '<div class="form-group">' +
                                    '<input type="text" ' + ((!value.additional_purpose_count > 0) ? 'required' : '') + ' class="form-control" name="additional_field[' + value.id + ']"' +
                                    'placeholder="' + value.field_name + '" autocomplete="off" value="' + value.info + '">' +
                                    '</div>' +
                                    '</div>';
                            } else {
                                add_content = add_content + '<div class="md-form mb-1 col-md-6 ' + status + ' prps_' + value.id + '">' +
                                    '<label>' + value.field_name + '</label>' +
                                    '<div class="form-group">' +
                                    '<input type="text" class="form-control" name="additional_field[' + value.id + ']"' +
                                    'placeholder="' + value.field_name + '" autocomplete="off" value="' + value.info + '">' +
                                    '</div>' +
                                    '</div>';
                            }
                        } else if (value.input_type == 2) {
                            if (value.is_required == 1) {
                                add_content = add_content + '<div class="md-form mb-1 col-md-6 ' + status + ' prps_' + value.id + '">' +
                                    '<label for="default-input" class="form-control-label">' + value.field_name + '</label>' +
                                    '<select ' + ((!value.additional_purpose_count > 0) ? 'required' : '') + ' class="form-control select2" name="additional_field[' + value.id + ']"id="enquiryTypeSelect">' +
                                    '<option value="">Select ' + value.field_name + '</option>';
                                $.each(value.values, function (key, valueI) {
                                    if (valueI == value.info) {
                                        add_content = add_content + '<option value = "' + valueI + '" selected>' + valueI + '</option>';
                                    } else {
                                        add_content = add_content + '<option value = "' + valueI + '">' + valueI + '</option>';
                                    }

                                });
                                add_content = add_content + '</select></div>';
                            } else {
                                add_content = add_content + '<div class="md-form mb-1 col-md-6 ' + status + ' prps_' + value.id + '">' +
                                    '<label for="default-input" class="form-control-label">' + value.field_name + '</label>' +
                                    '<select class="form-control select2" name="additional_field[' + value.id + ']" id="enquiryTypeSelect">' +
                                    '<option value="">Select ' + value.field_name + '</option>';
                                $.each(value.values, function (key, valueI) {
                                    if (valueI == value.info) {
                                        add_content = add_content + '<option value = "' + valueI + '" selected>' + valueI + '</option>';
                                    } else {
                                        add_content = add_content + '<option value = "' + valueI + '">' + valueI + '</option>';
                                    }

                                });
                                add_content = add_content + '</select></div>';
                            }
                        } else if (value.input_type == 8) {//Multi Select

                            if (value.is_required == 1) {
                                add_content = add_content + '<div class="md-form mb-1 col-md-6 ' + status + ' prps_' + value.id + '">' +
                                    '<label for="default-input" class="form-control-label">' + value.field_name + '</label>' +
                                    '<select ' + (value.is_required == 1 ? '' : ((!value.additional_purpose_count > 0) ? 'required' : '')) + ' multiple class="form-control select2" style="width:100%" name="additional_field[' + value.id + '][]" id="enquiryTypeSelect">' +
                                    '<option value="">Select' + value.field_name + '</option>';
                                $.each(value.values, function (key, valueI) {
                                    if (value.info != '' && JSON.parse(value.info).includes(valueI)) {
                                        add_content = add_content + '<option value = "' + valueI + '" selected>' + valueI + '</option>';
                                    } else {
                                        add_content = add_content + '<option value = "' + valueI + '">' + valueI + '</option>';
                                    }

                                });
                                add_content = add_content + '</select></div>';
                            } else {
                                add_content = add_content + '<div class="md-form mb-1 col-md-6 ' + status + ' prps_' + value.id + '">' +
                                    '<label for="default-input" class="form-control-label">' + value.field_name + '</label>' +
                                    '<select multiple class="form-control select2" style="width:100%" name="additional_field[' + value.id + '][]" id="enquiryTypeSelect">' +
                                    '<option value="">Select' + value.field_name + '</option>';
                                $.each(value.values, function (key, valueI) {
                                    if (value.info != '' && JSON.parse(value.info).includes(valueI)) {
                                        add_content = add_content + '<option value = "' + valueI + '" selected>' + valueI + '</option>';
                                    } else {
                                        add_content = add_content + '<option value = "' + valueI + '">' + valueI + '</option>';
                                    }

                                });
                                add_content = add_content + '</select></div>';
                            }
                        } else if (value.type_text == 'Image') {//Image
                            add_content = add_content + '<div class="md-form mb-1 col-md-6 ' + status + ' prps_' + value.id + '">' +
                                '<label for="default-input" class="form-control-label">' + value.field_name + '</label>' +
                                '<input ' + (value.is_required == 1 ? 'required' : '') + ' type="file"  class="form-control" style="width:100%" name="additional_field[' + value.id + ']" id="enquiryTypeSelect"></div>';
                            if (value.info != '') {
                                add_content = add_content + '<img src="' + value.info + '" width="50" height="50" class="img">';
                            }
                        } else if (value.input_type == 7) {//Phone  number
                            if (value.is_required == 1) {
                                add_content = add_content + '<div class="md-form mb-1 col-md-6 ' + status + ' prps_' + value.id + '">' +
                                    '<label>' + value.field_name + '</label>' +
                                    '<div class="form-group">' +
                                    '<input type="number" ' + ((!value.additional_purpose_count > 0) ? 'required' : '') + ' class="form-control" name="additional_field[' + value.id + ']"' +
                                    'placeholder="' + value.field_name + '" autocomplete="off" value="' + value.info + '">' +
                                    '</div>' +
                                    '</div>';
                            } else {
                                add_content = add_content + '<div class="md-form mb-1 col-md-6 ' + status + ' prps_' + value.id + '">' +
                                    '<label>' + value.field_name + '</label>' +
                                    '<div class="form-group">' +
                                    '<input type="number" class="form-control" name="additional_field[' + value.id + ']"' +
                                    'placeholder="' + value.field_name + '" autocomplete="off" value="' + value.info + '">' +
                                    '</div>' +
                                    '</div>';
                            }
                        } else if (value.input_type == 4) {
                            if (value.is_required == 1) {
                                add_content = add_content + '<div class="md-form mb-1 col-md-6 ' + status + ' prps_' + value.id + '">' +
                                    '<label>' + value.field_name + '</label>' +
                                    '<div class="form-group">' +
                                    '<input ' + ((!value.additional_purpose_count > 0) ? 'required' : '') + ' type="time" class="form-control" name="additional_field[' + value.id + ']"' +
                                    'placeholder="' + value.field_name + '" autocomplete="off" value="' + value.info + '">' +
                                    '</div>' +
                                    '</div>';
                            } else {
                                add_content = add_content + '<div class="md-form mb-1 col-md-6 ' + status + ' prps_' + value.id + '">' +
                                    '<label>' + value.field_name + '</label>' +
                                    '<div class="form-group">' +
                                    '<input type="time" class="form-control" name="additional_field[' + value.id + ']"' +
                                    'placeholder="' + value.field_name + '" autocomplete="off" value="' + value.info + '">' +
                                    '</div>' +
                                    '</div>';
                            }
                        } else {
                            if (value.is_required == 1) {
                                add_content = add_content + '<div class="md-form mb-1 col-md-6 ' + status + ' prps_' + value.id + '">' +
                                    '<label>' + value.field_name + '</label>' +
                                    '<div class="form-group">' +
                                    '<input ' + ((!value.additional_purpose_count > 0) ? 'required' : '') + ' type="date" class="form-control" name="additional_field[' + value.id + ']"' +
                                    'placeholder="' + value.field_name + '" autocomplete="off" value="' + value.info + '">' +
                                    '</div>' +
                                    '</div>';
                            } else {
                                add_content = add_content + '<div class="md-form mb-1 col-md-6 ' + status + ' prps_' + value.id + '">' +
                                    '<label>' + value.field_name + '</label>' +
                                    '<div class="form-group">' +
                                    '<input type="date" class="form-control" name="additional_field[' + value.id + ']"' +
                                    'placeholder="' + value.field_name + '" autocomplete="off" value="' + value.info + '">' +
                                    '</div>' +
                                    '</div>';
                            }
                        }
                        //
                    });
                    $('#add_details').append(add_content);
                    $('.select2').select2();
                    $('#enquiryTypeSelectMuliple').select2().trigger('change');
                    $("[name='more_phone_numbers']").val(data.more_phone_numbers);
                    $("[name='vchr_enquiry_feedback']").val(data.vchr_enquiry_feedback);
                    $("[name='fk_int_enquiry_type_id']").val(data.fk_int_enquiry_type_id);
                    $("[name='fk_int_purpose_id']").val(data.fk_int_purpose_id);
                    $("[name='date_of_birth']").val(data.date_of_birth);
                    $("[name='feedback_status']").val(data.feedback_status);
                    $("[name='address']").val(data.address);
                    $("[name='designation_id']").val(data.designation_id);
                    $("[name='lead_type_id']").val(data.lead_type_id);
                    $("[name='purchase_date']").val(data.purchase_date);
                    $("[name='exp_wt_grams']").val(data.exp_wt_grams);
                    $("[name='function_date']").val(data.function_date);

                    getAdditionalData(data.fk_int_purpose_id);
                    $("#enquirySubmit")[0].reset();
                    $('.error').html('');
                    $('.error').hide();
                }
            }).fail(function () {
            }).always(function () {
            });
        }

        function clearFilter() {
            $("#getReport")[0].reset();

            // Create a new daterangepicker instance with your desired configuration
            $('#all_date').daterangepicker({
                startDate: moment().startOf('month'),
                endDate: moment(),
                locale: {
                    format: 'YYYY-MM-DD'
                },
                ranges: {
                    'Today': [moment(), moment()],
                    'All time': [moment('2019-01-01'), moment()],
                    'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                    'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                    'This Month': [moment().startOf('month'), moment().endOf('month')],
                    'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
                }
            });


            if ($('#all_date').val() != '') {
                var all_date = $('#all_date').val().split(' - ');
                from_date = all_date[0];
                to_date = all_date[1];
            }

            $('.select2').select2();
            $('.select2').select2().trigger('change');
            $('#enquiry-info-table').DataTable().ajax.reload(null, false);
        }

        function deleteEnquiry(enquiryId) {
            var destinationPath = BASE_URL + '/user/enquiries/' + enquiryId + '{{( request("campaign_id") ? "?campaign_id=".request("campaign_id") : "" )}}';
            $.confirm({
                title: 'Deletion',
                content: 'Are you sure you want to delete ?',
                icon: 'la la-question-circle',
                animation: 'scale',
                closeAnimation: 'scale',
                opacity: 0.5,
                buttons: {
                    'confirm': {
                        text: 'Proceed',
                        btnClass: 'btn-info',
                        action: function () {
                            $.ajax({
                                url: destinationPath,
                                type: 'DELETE',
                            }).done(function (res) {
                                if (res.status == 'success') {
                                    $.alert({
                                        title: 'Success',
                                        type: 'green',
                                        content: res.msg,
                                    });
                                    $('#enquiry_detail').modal('hide');
                                } else {
                                    $.alert({
                                        title: 'Failed',
                                        type: 'red',
                                        content: res.msg,
                                    });
                                }
                            }).fail(function (err) {
                            }).always(function (com) {
                                $('#enquiry-info-table').DataTable().ajax.reload(null, false);
                            });
                        }
                    },
                    cancel: function () {
                        $.alert('Operation <strong>canceled</strong>');
                    }
                }
            });
        }

        function fetchFollowupCount(date) {
            BASE_URL = {!! json_encode(url('/')) !!}
            $.ajax({
                url: BASE_URL + '/user/followupcount',
                type: 'POST',
                data: {
                    'data': date
                },
                dataType: 'JSON'
            }).done(function (res) {
                if (res.status == 'success') {
                    $('.follow_count').html(res.data)
                    $('.date_text').html(date)
                } else {
                }
            }).fail(function (err) {
            });
        }

        /* -------- Start Purpose vise additional field ----- */
        $('#enquiryPurposeId').on('change', function () {
            value = $(this).val();
            getAdditionalData(value);
        });

        /* -------- Start Purpose vise additional field ----- */
        $('.enquiryPurposeSelect').on('change', function () {
            value = $(this).val();
            getAdditionalData(value);
        });

        /* -------- Start Purpose vise additional field ----- */
        function getAdditionalData(value) {
            BASE_URL = window.location.origin;
            $.ajax({
                url: BASE_URL + '/user/check-additional-field-purpose/' + value,
                type: 'GET',
                dataType: 'JSON',
            })
                .done(function (res) {
                    if (res.status) {
                        res.data.checked.forEach(element => {
                            $('.prps_' + element).removeClass('d-none');
                            $('.prps_' + element).children('.form-group').children('.form-control').attr('required', '');
                        });
                        res.data.unchecked.forEach(element => {
                            $('.prps_' + element).addClass('d-none');
                            $('.prps_' + element).children('.form-group').children('.form-control').removeAttr('required')
                        });
                    }

                })
                .fail(function () {
                });
        }
    </script>
@endpush