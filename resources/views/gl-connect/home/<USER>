@extends('backend.layout.master')
@section('page-header')
<link rel="stylesheet" type="text/css" href="{{ url('backend/css/pages/glconnect/glconnect.css')}}">
<style>
   .token-generate{
      display: flex;
      justify-content: center;
      align-items: center;
   }
</style>
@endsection
@section('content')
   <main class="main-wrapper">
        <div>
         <div class="">
            {{-- backend.layout.glconnectsidebar --}}
         {{-- @include ('backend.layout.sidebar-v2.glconnect-sidebar') --}}
         @include ('backend.layout.sidebar-v2.crmsidebar-v2')
         </div>
            <div class="content content-section d-flex flex-column flex-column-fluid">
               <div class="d-flex flex-column-fluid">
                  <!--begin::Container-->
                  <div class="connect" style="width: 100%">
                     <div class="row">
                        <div class="col-lg-12">
                           <div class="card connect-banner">
                              <div class="row">
                                 <div class="col-lg-10">
                                    <div style="float:left;width: 80%;"><h3>Connect</h3>
                                       <p>Getlead connects with tools like Telegram, Gupshup, Voxbay, and Other wonderful tools to make your  sales journey even easier. See the full list of integrations.</p>
                                    </div>
                                    <img src="{{asset('backend/media/connect.svg')}}"/>
                                 </div>
                                 <div class="col-lg-2 token-generate">
                                    <button type="button" class="generate-token main-round-btn green-btn btn" id="token-generate"
                                          data-toggle="modal" data-target="#ks-izi-modal-large">
                                       Generate API Token
                                    </button>
                                 </div>
                              </div>
                           </div>
                        </div>

                     </div>
                     <div class="row">
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/api.png')}}"/>
                                 </div>
                                 <div>
                                    <h4>Lead Generation API</h4>
                                    <span>Configure developer API</span>
                                 </div>
                                 <p>The GetLead CRM API enables developers to configure and integrate seamlessly, enhancing data exchange, automating processes, and optimizing lead management. This allows businesses to achieve efficient and tailored CRM functionality for the process.</p>
                                 {{-- <input type="checkbox" name="check-1" value="1" class="lcs_check" id="apiConfig" autocomplete="off" disabled checked/> --}}
                                 <a class="configure" href="{{url('/user/show-gl-website-contacts')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        {{-- @if(in_array(auth()->user()->pk_int_user_id, [51,196,786,4860])) --}}
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner has-actions">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/facebook.png')}}" width="96"/>
                                 </div>
                                 <div>
                                    <h4>Facebook</h4>
                                    <span>Social media platform</span>
                                 </div>
                                 <p>Facebook is a powerful social media platform that empowers businesses to connect and engage with their customers. With seamless integration with GetLead CRM, Facebook streamlines lead management, enhances customer interactions, and facilitates efficient communication.</p>
                                 <div class="card-actions">
                                    <a class="configure primary" href="{{url('/v1/facebook')}}">
                                       <i class="fa fa-cog fa-sm"></i> Configure
                                    </a>
                                    <a class="configure secondary" href="{{url('/v1/facebook/lead-requests')}}">
                                       <i class="fa fa-eye fa-sm"></i> View Leads
                                    </a>
                                 </div>
                              </div>
                           </div>
                        </div>
                        {{-- @endif --}}
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/telinfy.png')}}" width="96"/>
                                 </div>
                                 <div>
                                    <h4>Telinfy</h4>
                                    <span>Messaging platform</span>
                                 </div>
                                 <p>Telinfy is a powerful messaging platform that empowers businesses to connect and engage with their customers. With seamless integration with GetLead CRM, Telinfy streamlines lead management, enhances customer interactions, and facilitates efficient communication.</p>
                                 {{-- <input type="checkbox" name="check-1" value="1" class="lcs_check" autocomplete="off" disabled checked/> --}}
                                 <a class="configure" href="{{url('/user/telinfy')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/chatspaz.png')}}"/>
                                 </div>
                                 <div>
                                    <h4>Chatspaz</h4>
                                    <span>Messaging platform</span>
                                 </div>
                                 <p>Chatspaz is a messaging platform that enables smooth communication and interaction between businesses and customers. It integrates with GetLead CRM, making lead management easier and improving customer relationship management processes.</p>
                                 {{-- <input type="checkbox" name="check-1" value="1" class="lcs_check" autocomplete="off" disabled checked/> --}}
                                 <a class="configure" href="{{url('/user/chatspaz')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/interakt.png')}}" width="80" height="32"/>
                                 </div>
                                 <div>
                                    <h4>Interakt</h4>
                                    <span>Messaging platform</span>
                                 </div>
                                 <p>
                                    Interakt is a messaging platform that facilitates communication and engagement with customers. It integrates with GetLead CRM, simplifying lead management, improving customer interactions, and enabling collaboration between businesses and clients.</p>
                                    <br>
                                    {{-- <input type="checkbox" name="check-1" value="1" class="lcs_check" autocomplete="off" disabled checked/> --}}
                                 <a class="configure" href="{{url('/user/interakt')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/waba.png')}}"/>
                                 </div>
                                 <div>
                                    <h4>WABA</h4>
                                    <span>Messaging platform</span>
                                 </div>
                                 <p>GetLead CRM's integration with WhatsApp streamlines customer communication by centralizing it in the CRM. It enables effective management of customer interactions, improving response times and delivering personalized services directly through WhatsApp.</p>
                                 <br>
                                 <a class="configure" href="{{url('/user/waba')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/libromi.png')}}" width="100"/>
                                 </div>
                                 <div>
                                    <h4>Libromi</h4>
                                    <span>Messaging platform</span>
                                 </div>
                                 <p>Libromi's integration with WhatsApp is customer communication by centralizing it within the CRM. This integration enables effective management of customer interactions, improving response times and delivering personalized services directly through WhatsApp.</p>
                                 <br>
                                 <a class="configure" href="{{url('/user/libromi')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/picky_assist.png')}}" width="100"/>
                                 </div>
                                 <div>
                                    <h4>Picky Assist</h4>
                                    <span>Messaging platform</span>
                                 </div>
                                 <p>GetLead CRM's integration with Picky Assist streamlines customer communication by centralizing it within the CRM. This integration enables effective management of customer interactions, improving response times and delivering personalized services directly through WhatsApp.</p>
                                 <br>
                                 <a class="configure" href="{{url('/user/picky-assist')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/wabis.png')}}" width="100"/>
                                 </div>
                                 <div>
                                    <h4>Wabis</h4>
                                    <span>Messaging platform</span>
                                 </div>
                                 <p>Wabis' integration with GetLead CRM streamlines customer communication by centralizing it within the CRM. This integration enables effective management of customer interactions, improving response times and delivering personalized services directly through WhatsApp.</p>
                                 <br>
                                 <a class="configure" href="{{url('/user/wabis')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/doubletick.png')}}" width="100"/>
                                 </div>
                                 <div>
                                    <h4>DoubleTick</h4>
                                    <span>Messaging platform</span>
                                 </div>
                                 <p>DoubleTick' integration with GetLead CRM streamlines customer communication by centralizing it within the CRM. This integration enables effective management of customer interactions, improving response times and delivering personalized services directly through WhatsApp.</p>
                                 <br>
                                 <a class="configure" href="{{url('/user/doubletick')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/whatsapp.png')}}"/>
                                 </div>
                                 <div>
                                    <h4>WhatsApp</h4>
                                    <span>Messaging platform</span>
                                 </div>
                                 <p>GetLead CRM's integration with WhatsApp, through Gupshup and MessageBird, streamlines customer communication by centralizing it within the CRM. This enables effective interaction management, improves response times, and delivers personalized services directly through WhatsApp.</p>
                                 <br>
                                 <br>
                                 {{-- <input type="checkbox" name="check-1" value="1" class="lcs_check" autocomplete="off" /> --}}
                                 <a class="configure" href="{{url('/packages/watsapp-credential/index')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>

                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/gupshup.png')}}"/>
                                 </div>
                                 <div>
                                    <h4>Gupshup</h4>
                                    <span>Messaging platform</span>
                                 </div>
                                 <p>Gupshup is a versatile messaging platform designed to enhance customer communication and lead management. By integrating with CRM systems, Gupshup streamlines interactions, making it easier for businesses to collaborate with clients. This integration helps businesses manage leads more effectively, ensuring better customer engagement and improved overall efficiency in client.</p>
                                 <br>
                                 {{-- <input type="checkbox" name="check-1" value="1" class="lcs_check" autocomplete="off" /> --}}
                                 <a class="configure" href="{{url('/packages/watsapp-credential/index')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/shiprocket.png')}}"/>
                                 </div>
                                 <div>
                                    <h4>Shiprocket</h4>
                                    <span>Shipping software solution</span>
                                 </div>
                                 <p>Shiprocket integrates with GetLead CRM to streamline shipping operations and order management. This seamless integration enhances the customer experience by enabling efficient handling of customer data and lead information. It provides businesses with live tracking capabilities, ensuring transparency and improving overall customer satisfaction.</p>
                                 <input type="checkbox" name="check-1" value="1" class="lcs_check" autocomplete="off" disabled @if($shiprocket) checked @endif/>
                                 <a class="configure" href="{{url('/packages/shiprocket/settings')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/webhook.png')}}"/>
                                 </div>
                                 <div>
                                    <h4>WebHooks</h4>
                                    <span>Reverse API</span>
                                 </div>
                                 <p>WebHooks is a reverse API solution that integrates with GetLead CRM, enabling real-time data notifications and updates. By utilizing WebHooks, businesses can streamline lead management and enhance customer engagement. The integration ensures efficient data synchronization, automates processes, and optimizes CRM functionality for improved business outcomes.</p>
                                 @if($webhooks)
                                    <input type="checkbox" name="check-1" value="1" class="lcs_check" autocomplete="off" @if($webhooks) checked @endif disabled/>
                                 @else
                                    <input type="checkbox" name="check-1" value="1" class="lcs_check" autocomplete="off" disabled/>
                                 @endif
                                 <a class="configure" href="{{url('/user/webhook')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/woo.png')}}"/>
                                 </div>
                                 <div>
                                    <h4>WooCommerce</h4>
                                    <span>eCommerce platform</span>
                                 </div>
                                 <p>WooCommerce is a robust eCommerce platform that seamlessly integrates with GetLead CRM. By connecting WooCommerce with GetLead CRM, businesses can efficiently manage customer orders, track sales data, and optimize lead management. This integration enhances the overall customer experience, streamlines business operations, and maximizes sales opportunities.</p>
                                 <input type="checkbox" name="check-1" value="1" class="lcs_check" autocomplete="off" disabled/>
                                 <a class="configure" href="{{url('/packages/woocommerce/settings')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/indiamart.png')}}"/>
                                 </div>
                                 <div>
                                    <h4>Indiamart</h4>
                                    <span>E-commerce</span>
                                 </div>
                                 <p>IndiaMART seamlessly integrates with GetLead CRM, optimizing lead management and enhancing customer engagement. This integration streamlines e-commerce operations, enabling businesses to effectively manage leads and nurture customer relationships. Together, IndiaMART and GetLead CRM drive growth and success in the e-commerce industry.</p>

                                 @if($indiaMart)
                                 <input type="checkbox" name="check-1" value="1" class="lcs_check" autocomplete="off" @if($indiaMart) checked @endif disabled/>
                                 @else
                                    <input type="checkbox" name="check-1" value="1" class="lcs_check" autocomplete="off" disabled/>
                                 @endif

                                 <a class="configure" href="{{url('/packages/indiamart/settings')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/voxbay.png')}}"/>
                                 </div>
                                 <div>
                                    <h4>Voxbay</h4>
                                    <span>IVR</span>
                                 </div>
                                 <p>Voxbay's IVR system integrates with GetLead CRM, providing efficient call management and lead tracking capabilities. With Voxbay IVR, businesses can automate customer interactions, route calls, and capture valuable lead data directly into GetLead CRM, streamlining lead management and enhancing customer engagement.</p>
                                 @if($is_ivr)
                                    <input type="checkbox" name="check-1" value="1" class="lcs_check" autocomplete="off" @if($voxbay) checked @endif disabled/>
                                 @else
                                    <input type="checkbox" name="check-1" value="1" class="lcs_check" autocomplete="off" disabled/>
                                 @endif
                                 <a class="configure" href="{{url('/user/cloud-telephony')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/bonvoice.png')}}"/>
                                 </div>
                                 <div>
                                    <h4>Bonvoice</h4>
                                    <span>IVR</span>
                                 </div>
                                 <p>Bonvoice's IVR system seamlessly integrates with GetLead CRM, enabling effective call management and lead tracking. With Bonvoice IVR, businesses can automate customer interactions, route calls, and capture essential lead data directly into GetLead CRM, enhancing lead management and optimizing customer engagement processes.</p>
                                 @if($is_ivr)
                                    <input type="checkbox" name="check-1" value="1" class="lcs_check" autocomplete="off" @if($bonvoice) checked @endif disabled/>
                                 @else
                                    <input type="checkbox" name="check-1" value="1" class="lcs_check" autocomplete="off" disabled/>
                                 @endif
                                 <a class="configure" href="{{url('/user/cloud-telephony')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/mailer_cloud.png')}}" width="120px"/>
                                 </div>
                                 <div>
                                    <h4>Mailer Cloud</h4>
                                    <span>Mailer</span>
                                 </div>
                                 <p>MailerCloud is a powerful cloud email delivery service integrating with CRMs, allowing businesses to send personalized transactional and marketing emails effortlessly, ensuring high deliverability and scalability.</p>
                                 <br>
                                 <br>
                                 <input type="checkbox" name="check-1" value="1" class="lcs_check" autocomplete="off" disabled @if($mailerCloud) checked @endif/>
                                 <a class="configure" href="{{url('/user/mailer-cloud')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/coremicron.png')}}" width="30"/>
                                 </div>
                                 <div>
                                    <h4>Coremicron</h4>
                                    <span>Accounting</span>
                                 </div>
                                 <p>Coremicron integrates with GetLead CRM, optimizing accounting processes and lead management. This seamless integration streamlines financial operations and enhances customer relationship management for business growth.</p>
                                 <input type="checkbox" name="check-1" value="1" class="lcs_check checkedTally" id="checkedTally" autocomplete="off" @if($checkTally) checked @endif/>
                                 {{-- <a class="configure" href="Javascript:void(0)" id="checkedTally"><i class="fa fa-cog fa-md"></i> Configure</a> --}}
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/happilee.jpg')}}" width="50"/>
                                 </div>
                                 <div>
                                    <h4>Happilee</h4>
                                    <span>Messaging platform</span>
                                 </div>
                                 <p>Happilee's integration with WhatsApp is customer communication by centralizing it within the CRM. This integration enables effective management of customer interactions, improving response times and delivering personalized services directly through WhatsApp.</p>
                                 <br>
                                 <a class="configure" href="{{url('/user/happilee')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                        <div class="col-lg-4">
                           <div class="card">
                              <div class="card-inner">
                                 <div class="connect-logo">
                                    <img src="{{asset('backend/media/gallabox.svg')}}" width="100"/>
                                 </div>
                                 <div>
                                    <h4>Gallabox</h4>
                                    <span>Messaging platform</span>
                                 </div>
                                 <p>Gallabox's integration with WhatsApp is customer communication by centralizing it within the CRM. This integration enables effective management of customer interactions, improving response times and delivering personalized services directly through WhatsApp.</p>
                                 <br>
                                 <a class="configure" href="{{url('/user/gallabox')}}"><i class="fa fa-cog fa-md"></i> Configure</a>
                              </div>
                           </div>
                        </div>
                     </div>
                     <!--end::Row-->
                     <!--end::Dashboard-->
                  </div>
                  <!--end::Container-->
               </div>
         </div>
      </div>
      </div>
   </main>
   <!------------ /api modal/  -------------->

   <div class="modal fade" id="ks-izi-modal-large" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
      <form id="enquirySubmit">
         <div class="modal-dialog" role="document">
            <div class="modal-content">
               <div class="modal-header text-center">
                     <h4 class="modal-title w-100 font-weight-bold g-clr">Your Token Number</h4>
                     <!-- <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                     </button> -->
               </div>
               <div class="modal-body mx-3">
                     <div class="md-form mb-1">
                        <div class="input-group with-addon-icon-left">
                           <input type="text" class="copy-content form-control" id="api-token" placeholder=""
                                    readonly>
                           <span class="input-group-append">
                              <span class="input-group-text">
                                 <a class="copy" href="#">
                                    <i class="fa fa-files-o" aria-hidden="true"></i>
                                 </a>
                              </span>
                           </span><span class="message"></span>
                        </div>
                     </div>
                     <div class="modal-footer d-flex justify-content-center">
                        <button class="btn btn-deep-orange demo-btn" data-dismiss="modal">Close</button>
                     </div>
               </div>
            </div>
         </div>
      </form>
   </div>

@endsection
@push('footer.script')
<script type="text/javascript" src="{{ asset('backend/js/lc_switch.js')}}"></script>
<script type="text/javascript">
    const checkboxes = 'input[type=checkbox]',
          radios = 'input[type=radio]';

    // initialization
    lc_switch(checkboxes);
    lc_switch(radios, {
        compact_mode : true,
        on_color : 'teal',
    });

    document.querySelectorAll('input[id=checkedTally], input[id=checkedTally]').forEach(function(el) {
      el.addEventListener('lcs-on',function(){
         changeTallyOption(1)
      });

      el.addEventListener('lcs-off',function(){
         changeTallyOption(0)
      });
   });

   function changeTallyOption(value){
      url = @json(route('change-tally-data'));
      $.ajax({
         type: "GET",
         url: url,
         data: {
            'value' : value
         },
         dataType: "json",
         success: function (response) {
            status = (value) ? 'activated' : 'deactivated';
            if(response.status)
               toastr.success('Tally status '+status)
            else
               toastr.warning('Somthing went wrong!')
         }
      });
   }


   //  // enable/disable
   //  document.getElementById('enable_ckb').addEventListener('click', () => { lcs_enable(checkboxes); });
   //  document.getElementById('disable_ckb').addEventListener('click', () => { lcs_disable(checkboxes); });

   //  document.getElementById('enable_radio').addEventListener('click', () => { lcs_enable(radios); });
   //  document.getElementById('disable_radio').addEventListener('click', () => { lcs_disable(radios); });



   //  // events log
   //  const log_wrap = document.querySelector('#third_div ul');

   //  const toLeadingZero = (val) => {
   //      return (val.toString().length < 2) ? '0'+val : val;
   //  };

   //  const getTime = () => {
   //      const d = new Date;
   //      return '('+ toLeadingZero(d.getHours()) +':'+ toLeadingZero(d.getMinutes()) +':'+ toLeadingZero(d.getSeconds()) +')';
   //  };

   //  document.querySelectorAll('input').forEach(function(el) {
   //      const subj = el.getAttribute('type') +' #'+ el.value;

   //      el.addEventListener('lcs-statuschange', (e) => {
   //          let status = (el.checked) ? 'checked' : 'unchecked';
   //          status += (el.disabled) ? ' disabled' : ' enabled';

   //          log_wrap.innerHTML = '<li><em>'+ getTime() +' [lcs-statuschange]</em>'+ subj +' changed status: '+ status +'</li>' + log_wrap.innerHTML;
   //      });

   //      el.addEventListener('lcs-on', (e) => {
   //          const status = (el.checked) ? 'checked' : 'unchecked';
   //          log_wrap.innerHTML = '<li><em>'+ getTime() +' [lcs-on]</em>'+ subj +' changed status: '+ status +'</li>' + log_wrap.innerHTML;
   //      });

   //      el.addEventListener('lcs-off', (e) => {
   //          const status = (el.checked) ? 'checked' : 'unchecked';
   //          log_wrap.innerHTML = '<li><em>'+ getTime() +' [lcs-off]</em>'+ subj +' changed status: '+ status +'</li>' + log_wrap.innerHTML;
   //      });

   //      el.addEventListener('lcs-enabled', (e) => {
   //          const status = (el.disabled) ? 'disabled' : 'enabled';
   //          log_wrap.innerHTML = '<li><em>'+ getTime() +' [lcs-enabled]</em>'+ subj +' changed status: '+ status +'</li>' + log_wrap.innerHTML;
   //      });

   //      el.addEventListener('lcs-disabled', (e) => {
   //          const status = (el.disabled) ? 'disabled' : 'enabled';
   //          log_wrap.innerHTML = '<li><em>'+ getTime() +' [lcs-disabled]</em>'+ subj +' changed status: '+ status +'</li>' + log_wrap.innerHTML;
   //      });
   //  });


   //  // clear events log
   //  document.querySelector('#third_div small').addEventListener('click', () => {
   //      log_wrap.innerHTML = '';
   //  });

         $(".copy").click(function (event) {
            var copyText = document.getElementById("api-token");
            copyText.select();
            document.execCommand("copy");
            $(".message").text("copied");
            $('.message').delay(500).fadeOut();
         });
         $('#token-generate').on('click', function (event) {
            jQuery.ajax({
                url: "{{url('/generate-apitoken')}}", //php
                data: "", //the data "caller=name1&&callee=name2"
                dataType: 'json', //data format
                success: function (data) {
                    //on receive of reply
                    $('#api-token').empty();
                    $('#api-token').val(data.token);
                }
            });
         });
    </script>
@endpush
