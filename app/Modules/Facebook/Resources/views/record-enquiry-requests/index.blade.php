@extends('backend.layout.master')
@section('page-header')
<title>Facebook Leads</title>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css"/>
@include('facebook::style')
<style>
    .card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }
    .card:hover {
        transform: translateY(-5px);
    }
    .opacity-50 {
        opacity: 0.5;
    }
    .card-title {
        font-size: 1rem;
        font-weight: 600;
    }
    #stats-cards .card-body {
        padding: 1.5rem;
    }
    #stats-cards h2 {
        font-size: 2.2rem;
        font-weight: 700;
    }
</style>
@endsection
@section('content')
<main class="main-wrapper">
    <div class="task-panel">
        <div class="row justify-content-between">
            <div class="add-y-team">
                <div class="dashboard-row">
                    <div class="y-team-header y-team-header-V2">
                        <a href="{{ url('user/gl-connect') }}">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="25" viewBox="0 0 24 25" fill="none">
                                <path d="M19 12.5H5" stroke="#2E3A59" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M12 19.5L5 12.5L12 5.5" stroke="#2E3A59" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </a>
                        <h5>Facebook Lead Requests</h5>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="layout-wrapper">
        <!--   / Side menu included /  -->
        @include ('backend.layout.sidebar-v2.glconnect-sidebar')
        <div class="content-section p-3 bg-white">
            <!--  /Your content goes here/ -->
            <section class="main-sec">
                <!-- Filter Section -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Filter Options</h5>
                                <div class="row align-items-end">
                                    <div class="col-md-4">
                                        <div class="form-group mb-0">
                                            <label for="date_filter">Date Range</label>
                                            <div class="input-group with-addon-icon-left" id="date_range">
                                                <input type="text" class="form-control date_picker"
                                                    placeholder="Date" name="date_filter" autocomplete="off"
                                                    id="date_filter">
                                                <span class="input-group-append">
                                                    <span class="input-group-text">
                                                        <i class="fa fa-calendar"></i>
                                                    </span>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-primary w-100" id="filter_btn" style="line-height: 2.0">
                                            <i class="fa fa-filter"></i> Filter
                                        </button>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="button" class="btn btn-secondary w-100" id="clear_filter_btn" style="line-height: 2.0">
                                            <i class="fa fa-times"></i> Clear
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards Section -->
                <div class="row mb-4" id="stats-cards">
                    <div class="col-md-4 mb-3">
                        <div class="card bg-primary text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">Total Records</h6>
                                        <h2 class="mb-0" id="total-records">{{ $stats['total_records'] }}</h2>
                                    </div>
                                    <div>
                                        <i class="fa fa-file-alt fa-3x opacity-50"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card bg-success text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">Success Records</h6>
                                        <h2 class="mb-0" id="success-records">{{ $stats['success_count'] }}</h2>
                                    </div>
                                    <div>
                                        <i class="fa fa-check-circle fa-3x opacity-50"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card bg-danger text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">Failed Records</h6>
                                        <h2 class="mb-0" id="failed-records">{{ $stats['failed_count'] }}</h2>
                                    </div>
                                    <div>
                                        <i class="fa fa-times-circle fa-3x opacity-50"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card bg-success text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">New Records</h6>
                                        <h2 class="mb-0" id="new-records">{{ $stats['new_leads_count'] }}</h2>
                                    </div>
                                    <div>
                                        <i class="fa fa-plus-circle fa-3x opacity-50"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card bg-info text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">Duplicate Records</h6>
                                        <h2 class="mb-0" id="duplicate-records">{{ $stats['duplicate_leads_count'] }}</h2>
                                    </div>
                                    <div>
                                        <i class="fa fa-copy fa-3x opacity-50"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="card bg-warning text-white h-100">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title">Pending Records</h6>
                                        <h2 class="mb-0" id="pending-lead-data-records">{{ $stats['pending_lead_data_count'] }}</h2>
                                    </div>
                                    <div>
                                        <i class="fa fa-clock fa-3x opacity-50"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data Table Section -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="gl-table gl-table-v2">
                            <table id="record-enquiry-table" class="table table-striped table-bordered nowrap table-custom" cellspacing="0" width="100%">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Phone Number</th>
                                        <th>Additional Data</th>
                                        <th>Status</th>
                                        <th>Lead Status</th>
                                        <th>Created At</th>
                                        <th>Failure Reason</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
</main>
@endsection

@push('footer.script')
<script type="text/javascript" src="{{ url('backend/js/daterangepicker.js')}}"></script>
<script type="text/javascript">
    $(document).ready(function() {
        // Initialize date range picker
        var start = moment().startOf('day');
        var end = moment().endOf('day');

        function cb(start, end) {
            $('#date_filter span').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
        }

        $('#date_filter').daterangepicker({
            startDate: start,
            endDate: end,
            locale: {
                format: 'YYYY-MM-DD'
            },
            ranges: {
                'Today': [moment().startOf('day'), moment().endOf('day')],
                'Yesterday': [moment().subtract(1, 'days').startOf('day'), moment().subtract(1, 'days').endOf('day')],
                'Last 7 Days': [moment().subtract(6, 'days').startOf('day'), moment().endOf('day')],
                'Last 30 Days': [moment().subtract(29, 'days').startOf('day'), moment().endOf('day')],
                'This Month': [moment().startOf('month'), moment().endOf('month')],
                'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            }
        }, cb);

        cb(start, end);

        // Initialize DataTable
        var table = $('#record-enquiry-table').DataTable({
            processing: true,
            serverSide: true,
            searching: false,
            ordering: false,
            scrollX: true,
            ajax: {
                url: "{{ route('v1.facebook.lead-requests.data') }}",
                data: function(d) {
                    var dateRange = $('#date_filter').val().split(' - ');
                    d.from_date = dateRange[0];
                    d.to_date = dateRange[1];
                }
            },
            columns: [
                { data: 'id', name: 'id' },
                { data: 'name', name: 'name' },
                { data: 'phone_number', name: 'phone_number' },
                { data: 'lead_data', name: 'lead_data' },
                { data: 'status', name: 'status' },
                { data: 'lead_status', name: 'lead_status' },
                { data: 'created_at', name: 'created_at' },
                { data: 'failure_reason', name: 'failure_reason' }
            ],
            order: [[6, 'desc']],
        });

        // Function to update statistics
        function updateStatistics() {
            var dateRange = $('#date_filter').val().split(' - ');
            $.ajax({
                url: "{{ route('v1.facebook.lead-requests.stats') }}",
                type: "GET",
                data: {
                    from_date: dateRange[0],
                    to_date: dateRange[1]
                },
                success: function(response) {
                    $('#total-records').text(response.total_records);
                    $('#success-records').text(response.success_count);
                    $('#failed-records').text(response.failed_count);
                    $('#new-records').text(response.new_leads_count);
                    $('#duplicate-records').text(response.duplicate_leads_count);
                    $('#pending-lead-data-records').text(response.pending_lead_data_count);

                    // Log the response for debugging
                    console.log('Statistics response:', response);
                },
                error: function(xhr) {
                    console.error('Error fetching statistics:', xhr);
                }
            });
        }

        // Filter button click
        $('#filter_btn').on('click', function() {
            table.ajax.reload();
            updateStatistics();
        });

        // Clear filter button click
        $('#clear_filter_btn').on('click', function() {
            var start = moment().startOf('day');
            var end = moment().endOf('day');

            $('#date_filter').data('daterangepicker').setStartDate(start);
            $('#date_filter').data('daterangepicker').setEndDate(end);

            table.ajax.reload();
            updateStatistics();
        });

        // Initialize tooltips
        $(document).on('mouseenter', '[data-toggle="tooltip"]', function() {
            $(this).tooltip('show');
        });

        // Close other tooltips when a new one is opened
        $(document).on('shown.bs.tooltip', function() {
            $('[data-toggle="tooltip"]').not(':hover').tooltip('hide');
        });

        // Update statistics on page load
        updateStatistics();
    });
</script>
@endpush
