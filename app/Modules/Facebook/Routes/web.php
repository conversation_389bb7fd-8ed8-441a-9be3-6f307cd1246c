<?php

use App\Modules\Facebook\Http\Controllers\FacebookController;
use App\Modules\Facebook\Http\Controllers\RecordEnquiryRequestController;
use App\Modules\Facebook\Http\Controllers\WebhookController;
use App\Modules\Facebook\Http\Controllers\WorkFlowController;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Route;

Route::prefix('v1/facebook')->as('v1.facebook')
    ->middleware(['web', 'checkSubscription'])
    ->group(function () {
        Route::get('/login', [FacebookController::class, 'login'])->name('.login');
        Route::post('/webhook', [FacebookController::class, 'webhook'])->name('.webhook');
        Route::get('callback', [FacebookController::class, 'callback'])->name('.callback');
        Route::get('create', [FacebookController::class, 'create'])->name('.create-workflow');
        Route::get('map-keys/{id}', [FacebookController::class, 'workflowMapKeys'])->name('.get-map-keys');
        Route::post('/map-keys', [FacebookController::class, 'submitMapKeys'])->name('.map-keys');

        // Work flow routes
        Route::get('/', [WorkFlowController::class, 'index'])->name('.welcome');
        Route::post('workflow', [WorkFlowController::class, 'workflow'])->name('.workflow');
        Route::get('workflow', [WorkFlowController::class, 'getWorkflow'])->name('.get-workflow');
        Route::post('workflow/update', [WorkFlowController::class, 'updateWorkflow'])->name('.update-workflow');
        Route::get('workflow/{id}', [WorkFlowController::class, 'destroy'])->name('.destroy');

        // Record Enquiry Request routes
        Route::get('lead-requests', [RecordEnquiryRequestController::class, 'index'])->name('.lead-requests');
        Route::get('lead-requests/data', [RecordEnquiryRequestController::class, 'getRecordEnquiryRequests'])->name('.lead-requests.data');
        Route::get('lead-requests/stats', [RecordEnquiryRequestController::class, 'getStatisticsData'])->name('.lead-requests.stats');
    });

Route::prefix('api/webhook/facebook')
    ->as('v1.facebook')
    ->middleware(['api'])->group(function () {
        Route::get('get-call-back/{token?}', [WebhookController::class, 'verifyWebhook'])->name('.verify-token');
//        Route::post('get-call-back', [WebhookController::class, 'handleWebhook'])->name('.handle-webhook');
    });

Route::prefix('api/facebook/')
    ->as('v1.facebook')
    ->middleware(['api'])->group(function () {
        Route::post('get-pages', [FacebookController::class, 'pageList'])->name('.page-list');
        Route::post('get-forms', [FacebookController::class, 'formList'])->name('.form-list');
        Route::post('get-map-keys', [FacebookController::class, 'getMapKeys'])->name('.map-keys');
        Route::post('get-connections', [FacebookController::class, 'getConnections'])->name('.get-connections');
        Route::delete('delete-connection/{id}', [FacebookController::class, 'deleteConnection'])->name('.delete-connection');
        Route::put('update-connection/{id}', [FacebookController::class, 'updateConnection'])->name('.update-connection');
    });

Route::get('/module-assets/{folder}/{filename}', function ($folder, $filename) {
    $path = storage_path('../app/Modules/Facebook/public/assets/' . $folder . '/' . $filename);

    if (file_exists($path)) {
        $mimeType = '';

        // Determine MIME type based on the file extension
        switch (pathinfo($filename, PATHINFO_EXTENSION)) {
            case 'css':
                $mimeType = 'text/css';
                break;
            case 'js':
                $mimeType = 'application/javascript';
                break;
            case 'jpg':
            case 'jpeg':
                $mimeType = 'image/jpeg';
                break;
            case 'png':
                $mimeType = 'image/png';
                break;
            // Add more cases as needed for other file types
            default:
                $mimeType = mime_content_type($path);
                break;
        }

        return Response::file($path, ['Content-Type' => $mimeType]);
    }

    abort(404);
});