<?php

declare(strict_types=1);

namespace App\Modules\Facebook\Services;

final class ErrorMessageMapper
{
    public function map(string $errorMessage): string
    {
        {
            if (preg_match('/Workflow not found for ad_id: \d+/', $errorMessage)) {
                return "We couldn't record this lead because the facebook form hasn't been linked to the CRM. Please set it up to continue.";
            }
            
            if (strpos($errorMessage, 'Facebook token has expired.') !== false) {
                return "We couldn't access your lead data. If you've changed your Facebook password recently, please reconnect your account.";
            }
            
            return $errorMessage;
        }

    }
}
