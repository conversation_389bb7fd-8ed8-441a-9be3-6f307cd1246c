<?php

namespace App\Modules\Facebook\Jobs\RecordEnquiry;

use Carbon\Carbon;
use App\BackendModel\Enquiry;
use Illuminate\Support\Facades\Log;
use App\FrontendModel\LeadAdditionalField;
use App\FrontendModel\LeadAdditionalDetails;
use App\FrontendModel\LeadAdditionalFieldType;

class RecordAdditionalAttributes
{
    public function for(Enquiry $enquiry, int $vendorId, array $metadata): void
    {
        $additionalFields = LeadAdditionalField::query()
            ->where('vendor_id', '=', $vendorId)
            ->get();

        Log::info('Lead additional fields', [
            'additional_fields' => $additionalFields->all()
        ]);

        $this->newApproach($additionalFields->all(), $enquiry, $vendorId, $metadata);
    }

    private function newApproach(array $additionalFields, Enquiry $enquiry, int $vendorId, array $metadata): void
    {
        $attributes = [];

        foreach ($additionalFields as $field) {
            $fieldValue = $this->findFieldValueInMetadata($field, $metadata);
            
            if ($fieldValue === null) {
                continue;
            }
            
            $formattedValue = $field->input_type == LeadAdditionalFieldType::MultiSelectDropdown
                ? json_encode(explode(',', $fieldValue))
                : $fieldValue;
            
            $attributes[] = [
                'enquiry_id' => $enquiry->pk_int_enquiry_id,
                'field_id' => $field->id,
                'value' => $formattedValue,
                'created_by' => $vendorId,
                'field_name' => $field->field_name,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];
        }

        $attributes = array_filter($attributes, fn (array $item) => $item['value'] !== null);
        
        Log::info('Lead additional fields mapped to be inserted', [
            'attributes' => $attributes
        ]);

        if (!empty($attributes)) {
            LeadAdditionalDetails::insert($attributes);
        }
    }

    /**
     * Find a field value in the metadata using a simplified matching approach
     */
    private function findFieldValueInMetadata(LeadAdditionalField $field, array $metadata): ?string
    {
        foreach ($metadata as $key => $value) {
            $formattedKey = $this->standardizeValue($key);
            $metadata[$formattedKey] = $value;
        }

        $fieldName = $this->standardizeValue($field->field_name);

        if ($field->input_type == LeadAdditionalFieldType::Dropdown->value || 
            $field->input_type == LeadAdditionalFieldType::MultiSelectDropdown->value) {
                
            if (empty($field->decoded_values)) {
                return null;
            }
            
            $standardizedDropdownValues = [];
            foreach ($field->decoded_values as $value) {

                $standardized = $this->standardizeValue($value);
                $standardizedDropdownValues[$standardized] = $value;
            }
            
            foreach ($metadata as $metaValue) {
                if (is_string($metaValue)) {
                    $standardizedMetaValue = $this->standardizeValue($metaValue);
                    
                    if (isset($standardizedDropdownValues[$standardizedMetaValue])) {
                        return $standardizedDropdownValues[$standardizedMetaValue];
                    }
                }

            }
        }
        
        if (isset($metadata[$fieldName])) {
            return $metadata[$fieldName];
        }

        return null;
    }
    
    /**
     * Standardize a value for comparison:
     * - Convert to uppercase
     * - Remove special characters (underscores, etc.)
     * - Trim whitespace
     */
    private function standardizeValue(string $value): string
    {
        $value = str_replace([' ', '_'], '', $value);

        // Convert to uppercase
        $standardized = strtoupper($value);
        
        // Remove trailing underscores
        $standardized = rtrim($standardized, '_');
        
        // Trim whitespace
        $standardized = trim($standardized);
        
        return $standardized;
    }
}