<?php

declare(strict_types=1);

namespace App\Modules\Facebook\Jobs\RecordEnquiry;

use Throwable;
use Illuminate\Support\Str;
use App\Common\Facades\Mediator;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\InteractsWithQueue;
use App\Modules\Facebook\Models\FbWorkFlow;
use App\Modules\Facebook\Models\LeadStatus;
use Illuminate\Contracts\Queue\ShouldQueue;
use Shared\Exceptions\PhoneNumberIsInvalid;
use App\Modules\Facebook\ValueObjects\LeadData;
use App\Modules\Facebook\Models\RecordEnquiryRequest;
use App\Modules\Facebook\Exceptions\FacebookTokenInvalid;
use App\Modules\Facebook\Exceptions\UnableToFetchLeadData;
use App\Modules\Facebook\UseCases\MarkRequestAsFailed\MarkRequestAsFailed;

final class ProcessRecordEnquiryRequest implements ShouldQueue
{
    use InteractsWithQueue;

    public string $queue = 'integration:meta';

    public function __construct(
        private readonly int $recordEnquiryId,
    ) {
    }

    public function handle(
        PrepareEnquiryToRecord $prepareEnquiryToRecord,
        PhoneNumberParserFactory $phoneNumberParserFactory
    ): void {
        Log::withContext([
            'record_enquiry_id' => $this->recordEnquiryId,
        ]);

        Log::info('Processing record enquiry request');

        $recordEnquiryRequest = $this->getRecordEnquiryRequest(requestId: $this->recordEnquiryId);

        /** @var FbWorkFlow|null $workflow */
        $workflow = $this->getWorkflow(
            pageId: (int) $recordEnquiryRequest->page_id,
            adId: (int) $recordEnquiryRequest->form_id
        );

        if (!$workflow instanceof FbWorkFlow) {
            $this->requestHasFailed(
                reason: 'Workflow not found for ad_id: ' . $recordEnquiryRequest->form_id
            );
            return;
        }

        try {
            $leadAttributes = $prepareEnquiryToRecord->for(
                leadGenId: (int) $recordEnquiryRequest->lead_gen_id,
                accessToken: $workflow->page_access_token,
                mappedFields: $workflow->mapped_keys
            );

            // Create a clean metadata array without the main fields
            $metadata = $leadAttributes;

            // Create the LeadData object
            $leadData = new LeadData(
                source: $this->getCaseInsensitiveValue($leadAttributes, 'source', 'Meta Leads'),
                name: $this->getCaseInsensitiveValue($leadAttributes, 'name', 'no name'),
                phoneNumber: $leadAttributes['mobileno'] ?? "",
                email: $this->getCaseInsensitiveValue($leadAttributes, 'email'),
                metadata: $metadata
            );
        
            $recordEnquiryRequest->update([
                'lead_data' => $leadData,
                'lead_status' => LeadStatus::Pending,
            ]);

            Bus::dispatch(new RecordEnquiry(
                recordEnquiryId: $this->recordEnquiryId,
                vendorId: $workflow->vendor_id,
                contact: new Contact(
                    name: $leadData->name,
                    phoneNumber: $phoneNumberParserFactory->for($workflow->vendor_id)
                        ->parse($leadData->phoneNumber),
                    email: $leadData->email,
                ),
                source: $leadData->source,
                metadata: $leadData->metadata
            ));

        } catch (PhoneNumberIsInvalid $e) {
            Log::info('Failed to parse phone number', [
                'phone_number' => $leadAttributes['mobileno'],
                'exception' => $e->getMessage(),
            ]);

            $this->requestHasFailed(reason: Str::limit($e->getMessage(), 200));
            return;
        } catch (FacebookTokenInvalid $e) {
            $this->requestHasFailed(reason: "Facebook token has expired.");
        } catch (UnableToFetchLeadData $e) {
            $this->requestHasFailed(reason: $e->getMessage());
            return;
        }
    }

    public function failed(Throwable $e): void
    {
        Log::info('Failed to process record enquiry request', [
            'record_enquiry_id' => $this->recordEnquiryId,
            'exception' => $e->getMessage(),
        ]);

        $this->requestHasFailed(Str::limit($e->getMessage(), 200));
    }

    private function requestHasFailed(string $reason): void
    {
        Mediator::dispatch(new MarkRequestAsFailed(id: $this->recordEnquiryId, reason: $reason));
    }

    private function getRecordEnquiryRequest(int $requestId): RecordEnquiryRequest
    {
        /** @var RecordEnquiryRequest|null */
        return RecordEnquiryRequest::query()
            ->where('id', $requestId)
            ->first(['id', 'page_id', 'form_id', 'lead_gen_id', 'lead_data', 'lead_status']);
    }

    private function getWorkflow(int $pageId, int $adId): ?FbWorkFlow
    {
        /** @var FbWorkFlow|null */
        return FbWorkFlow::query()
            ->where('fb_page_id', '=', $pageId)
            ->where('fb_ad_id', '=', $adId)
            ->where('active', '=', 1)
            ->first(['vendor_id', 'mapped_keys', 'page_access_token']);
    }

    private function getCaseInsensitiveValue(array $array, string $key, $default = null) {
        foreach ($array as $k => $v) {
            if (strcasecmp($k, $key) === 0) {
                return $v;
            }
        }
        return $default;
    }
}
