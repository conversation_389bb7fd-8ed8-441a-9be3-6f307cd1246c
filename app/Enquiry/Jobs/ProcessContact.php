<?php

declare(strict_types=1);

namespace App\Enquiry\Jobs;

use Throwable;
use Illuminate\Support\Str;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use App\Common\Facades\Mediator;
use Illuminate\Support\Facades\Log;
use App\Enquiry\Models\ImportContact;
use App\Enquiry\Models\ImportRequest;
use Illuminate\Queue\InteractsWithQueue;
use App\Enquiry\Enums\ImportContactStatus;
use Illuminate\Contracts\Queue\ShouldQueue;
use App\Enquiry\UseCases\CreateEnquiry\Contact;
use App\Enquiry\Exceptions\EnquiryAlreadyExists;
use Illuminate\Queue\Middleware\WithoutOverlapping;
use App\Enquiry\UseCases\CreateEnquiry\CreateEnquiry;
use App\Enquiry\UseCases\UpdateEnquiry\UpdateEnquiry;
use App\Modules\Facebook\Jobs\RecordEnquiry\PhoneNumber;
use Illuminate\Support\Arr;

class ProcessContact implements ShouldQueue
{
    use Batchable;
    use InteractsWithQueue;
    use Queueable;
    
    public int $tries = 3;

    public int $maxExceptions = 3;

    public int $backoff = 30;

    public function __construct(
        public readonly int $importContactId,
        public readonly int $importRequestId,
        public readonly int $vendorId,
    ) {
        $this->onQueue('lead-import');
    }

    /**
     * @return object[]
     */
    public function middleware(): array
    {
        return [new WithoutOverlapping((string) $this->importContactId)];
    }

    public function handle(): void
    {
        Log::withContext([
            'import_contact_id' => $this->importContactId,
            'import_request_id' => $this->importRequestId,
            'vendor_id' => $this->vendorId,
        ])->info('Processing import contact');

        if ($this->batch()->cancelled()) {
            Log::info('Import contact cancelled');
            $this->markImportAsFailed($this->importContactId, 'Import cancelled');
            return;
        }

        $importRequest = $this->getImportRequest($this->importRequestId);

        if (!$importRequest instanceof ImportRequest) {
            Log::error('Import request not found');
            return;
        }

        $importContact = $this->getImportContact(id: $this->importContactId);
        
        if (!$importContact instanceof ImportContact) {
            Log::error('Import contact not found');
            return;
        }
        
        if (! $importContact->isPending()) {
            Log::info('Import contact already processed', [
                'status' => $importContact->status->value
            ]);
            return;
        }

        $metadata = $this->parseMetadata($importContact->metadata ?? []);

        if( !(Arr::has($metadata, 'staff_name') && filled($metadata['staff_name']))) {
            $metadata['staff_id'] = $importRequest->created_by;
        }

        try {
            Mediator::dispatch(new CreateEnquiry(
                vendorId: $this->vendorId,
                source: $metadata['source'] ?? 'Excel import',
                contact: new Contact(
                    name: $importContact->name,
                    phoneNumber: PhoneNumber::parse($importContact->phone_number),
                    email: $importContact->email,
                ),
                createdBy: $importRequest->created_by,
                metadata: $metadata,
            ));
            
            $importContact->update([
                'status' => ImportContactStatus::Success,
            ]);
            
        } catch (EnquiryAlreadyExists) {
            Log::info('Contact already exists', [
                'import_contact_id' => $this->importContactId,
                'vendor_id' => $this->vendorId,
            ]);

            Mediator::dispatch(new UpdateEnquiry(
                vendorId: $this->vendorId,
                source: $metadata['source'] ?? 'Excel import',
                contact: new Contact(
                    name: $importContact->name,
                    phoneNumber: PhoneNumber::parse($importContact->phone_number),
                    email: $importContact->email,
                ),
                metadata: $metadata,
            ));

            $importContact->update([
                'status' => ImportContactStatus::Duplicate,
            ]);
            
        }
    }

    private function getImportRequest(int $importRequestId): ?ImportRequest
    {
        return ImportRequest::query()
            ->where('id', '=', $importRequestId)
            ->first(['id', 'created_by']);
    }

    public function getImportContact(int $id): ?ImportContact
    {
        return ImportContact::query()
            ->where('id', '=', $id)
            ->first();
    }

    public function failed(Throwable $exception): void
    {
        Log::error('Import contact failed', [
            'vendor_id' => $this->vendorId,
            'import_contact_id' => $this->importContactId,
            'reason' => $exception->getMessage(),
        ]);

        $this->markImportAsFailed(
            importContactId: $this->importContactId,
            reason: Str::limit($exception->getMessage(), 200)
        );
    }

    private function markImportAsFailed(int $importContactId, string $reason = ''): void
    {
        ImportContact::query()
            ->where('id', '=', $importContactId)
            ->update([
                'status' => ImportContactStatus::Failed,
                'reason' => $reason,
            ]);
    }

    private function parseMetadata(array $metadata): array
    {
        return array_filter([
            'source' => $metadata['lead_source'] ?? 'Excel import',
            'type' => $metadata['lead_type'] ?? '',
            'purpose' => $metadata['purpose'] ?? '',
            'status' => $metadata['status'] ?? '',
            'feedback' => $metadata['feedback'] ?? '',
            'more_phone_numbers' => $metadata['alternate_numbers'] ?? '',
            'company_name' => $metadata['company_name'] ?? '',
            'notes' => $metadata['lead_note'] ?? '',
            'department' => $metadata['department'] ?? '',
            ...$metadata,
        ]);
    }
}
