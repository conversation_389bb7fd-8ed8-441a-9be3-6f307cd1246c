<?php

declare(strict_types=1);

namespace Tests\Unit\Facebook\Services;

use Tests\TestCase;
use App\Modules\Facebook\Services\ErrorMessageMapper;

class ErrorMessageMapperTest extends TestCase
{
    private ErrorMessageMapper $errorMapper;

    protected function setUp(): void
    {
        parent::setUp();
        $this->errorMapper = new ErrorMessageMapper();
    }

    /** 
     * @test 
     */
    public function it_parses_facebook_token_errors_appropriately(): void
    {
        $error = 'Facebook token has expired.';

        $this->assertEquals(
            expected: "We couldn't access your lead data. If you've changed your Facebook password recently, please reconnect your account.", 
            actual: $this->errorMapper->map($error)
        );
    }

    /** 
     * @test 
     */
    public function it_parses_workflow_not_found_errors_appropriately(): void
    {
        $error = 'Workflow not found for ad_id: 12345';

        $this->assertEquals(
            expected: "We couldn't record this lead because the facebook form hasn't been linked to the CRM. Please set it up to continue.", 
            actual: $this->errorMapper->map($error)
        );
    }

    /** 
     * @test 
     */
    public function it_parses_other_errors_appropriately(): void
    {
        $error = 'unexpected error';

        $this->assertEquals(
            expected: $error, 
            actual: $this->errorMapper->map($error)
        );
    }
}
