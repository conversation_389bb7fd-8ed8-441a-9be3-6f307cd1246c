<?php

declare(strict_types=1);

namespace Tests\Unit\Enquiry\Jobs;

use App\Common\Facades\Mediator;
use App\Enquiry\Enums\ImportContactStatus;
use App\Enquiry\Exceptions\EnquiryAlreadyExists;
use App\Enquiry\Models\ImportContact;
use App\Enquiry\Models\ImportRequest;
use App\Enquiry\UseCases\CreateEnquiry\Contact;
use App\Enquiry\UseCases\CreateEnquiry\CreateEnquiry;
use App\Enquiry\UseCases\UpdateEnquiry\UpdateEnquiry;
use App\Modules\Facebook\Jobs\RecordEnquiry\PhoneNumber;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;
use Tests\Unit\Enquiry\Jobs\TestProcessContact as ProcessContact;

final class ProcessContactTest extends TestCase
{
    use DatabaseTransactions;

    private int $importContactId;
    private int $importRequestId;
    private int $vendorId;
    private ImportContact $importContact;
    private ImportRequest $importRequest;

    protected function setUp(): void
    {
        parent::setUp();

        $this->vendorId = 1;

        $this->importRequest = ImportRequest::create([
            'vendor_id' => $this->vendorId,
            'status' => 1, // InProgress
            'file_path' => 'test.csv',
            'created_by' => 10, // Staff ID
            'uuid' => 'test-uuid',
            'no_of_records' => 1,
            'no_of_duplicate_records' => 0,
            'no_of_imported_records' => 0,
            'no_of_invalid_records' => 0,
        ]);

        $this->importRequestId = $this->importRequest->id;

        $this->importContact = ImportContact::create([
            'import_request_id' => $this->importRequestId,
            'name' => 'Test Contact',
            'phone_number' => '+************',
            'email' => '<EMAIL>',
            'status' => ImportContactStatus::Pending,
            'metadata' => [
                'lead_source' => 'Test Source',
                'lead_type' => 'Test Type',
                'purpose' => 'Test Purpose',
                'status' => 'Test Status',
                'feedback' => 'Test Feedback',
                'alternate_numbers' => '+************',
                'company_name' => 'Test Company',
                'lead_note' => 'Test Note',
                'department' => 'Test Department',
            ],
        ]);

        $this->importContactId = $this->importContact->id;

        Mediator::fake([CreateEnquiry::class, UpdateEnquiry::class]);
    }

    /**
     * @test
     */
    public function it_dispatches_create_enquiry_when_contact_is_new(): void
    {
        $job = new ProcessContact(
            importContactId: $this->importContactId,
            importRequestId: $this->importRequestId,
            vendorId: $this->vendorId
        );

        $job->handle();

        Mediator::assertDispatched(CreateEnquiry::class, function (CreateEnquiry $request) {
            return $request->vendorId === $this->vendorId
                && $request->source === 'Test Source'
                && $request->contact instanceof Contact
                && $request->contact->name === 'Test Contact'
                && $request->contact->phoneNumber instanceof PhoneNumber
                && $request->contact->phoneNumber->toPhoneNumber() === '************'
                && $request->contact->email === '<EMAIL>'
                && $request->createdBy === $this->importRequest->created_by
                && $request->metadata['source'] === 'Test Source'
                && $request->metadata['type'] === 'Test Type'
                && $request->metadata['purpose'] === 'Test Purpose'
                && $request->metadata['status'] === 'Test Status'
                && $request->metadata['feedback'] === 'Test Feedback'
                && $request->metadata['more_phone_numbers'] === '+************'
                && $request->metadata['company_name'] === 'Test Company'
                && $request->metadata['notes'] === 'Test Note'
                && $request->metadata['department'] === 'Test Department'
                && $request->metadata['staff_id'] === 10;
        });

        $this->assertDatabaseHas(ImportContact::class, [
            'id' => $this->importContact->id,
            'status' => ImportContactStatus::Success->value,
        ]);
    }

    /**
     * @test
     */
    public function it_dispatches_update_enquiry_when_contact_already_exists(): void
    {
        Mediator::fake(CreateEnquiry::class, fn() => throw new EnquiryAlreadyExists());
        Mediator::fake(UpdateEnquiry::class);

        $job = new ProcessContact(
            importContactId: $this->importContactId,
            importRequestId: $this->importRequestId,
            vendorId: $this->vendorId
        );

        $job->handle();

        Mediator::assertDispatched(UpdateEnquiry::class, function (UpdateEnquiry $request) {
            return $request->vendorId === $this->vendorId
                && $request->source === 'Test Source'
                && $request->contact instanceof Contact
                && $request->contact->name === 'Test Contact'
                && $request->contact->phoneNumber instanceof PhoneNumber
                && $request->contact->phoneNumber->toPhoneNumber() === '************'
                && $request->contact->email === '<EMAIL>';
        });

        $this->assertDatabaseHas(ImportContact::class, [
            'id' => $this->importContact->id,
            'status' => ImportContactStatus::Duplicate->value,
        ]);
    }

    /**
     * @test
     */
    public function it_skips_processing_when_batch_is_cancelled(): void
    {
        $job = new ProcessContact(
            importContactId: $this->importContactId,
            importRequestId: $this->importRequestId,
            vendorId: $this->vendorId,
            batchCancelled: true
        );

        $job->handle();

        Mediator::assertNotDispatched(CreateEnquiry::class);
        Mediator::assertNotDispatched(UpdateEnquiry::class);

        $this->assertDatabaseHas(ImportContact::class, [
            'id' => $this->importContact->id,
            'status' => ImportContactStatus::Failed->value,
            'reason' => 'Import cancelled',
        ]);
    }

    /**
     * @test
     */
    public function it_skips_processing_when_import_request_not_found(): void
    {
        $nonExistentRequestId = $this->importRequestId + 1000;

        $job = new ProcessContact(
            importContactId: $this->importContactId,
            importRequestId: $nonExistentRequestId,
            vendorId: $this->vendorId
        );

        $job->handle();

        Mediator::assertNotDispatched(CreateEnquiry::class);
        Mediator::assertNotDispatched(UpdateEnquiry::class);
    }

    /**
     * @test
     */
    public function it_skips_processing_when_import_contact_not_found(): void
    {
        $nonExistentContactId = $this->importContactId + 1000;

        $job = new ProcessContact(
            importContactId: $nonExistentContactId,
            importRequestId: $this->importRequestId,
            vendorId: $this->vendorId
        );

        $job->handle();

        Mediator::assertNotDispatched(CreateEnquiry::class);
        Mediator::assertNotDispatched(UpdateEnquiry::class);
    }

    /**
     * @test
     */
    public function it_skips_processing_when_import_contact_is_not_pending(): void
    {
        $this->importContact->update([
            'status' => ImportContactStatus::Success,
        ]);

        $job = new ProcessContact(
            importContactId: $this->importContactId,
            importRequestId: $this->importRequestId,
            vendorId: $this->vendorId
        );

        $job->handle();

        Mediator::assertNotDispatched(CreateEnquiry::class);
        Mediator::assertNotDispatched(UpdateEnquiry::class);
    }
}
